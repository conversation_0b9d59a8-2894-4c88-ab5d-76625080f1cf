"""Utility functions used in our graph."""

from typing import Any, Dict
from datetime import datetime


def get_current_date_context() -> str:
    """Get current date context for prompts."""
    current_date = datetime.now()
    return f"Current date: {current_date.strftime('%B %d, %Y')}"


def convert_simple_schema_to_json_schema(extraction_schema: Dict[str, Any]) -> Dict[str, Any]:
    """Convert simple extraction schema format to JSON Schema format.
    
    Supports both simple and nested array schemas:
    
    Simple format:
    {
        "format": "json",
        "fields": {
            "field_name": "string",
            "field_name_2": "integer",
            "simple_array": "array"
        }
    }
    
    Nested array format:
    {
        "format": "json", 
        "fields": {
            "companies": {
                "type": "array",
                "items": {
                    "company_name": "string",
                    "industry": "string",
                    "hq_location": "string",
                    "short_description": "string"
                }
            }
        }
    }
    
    Converts to JSON Schema format with proper array item definitions:
    {
        "type": "object",
        "properties": {
            "companies": {
                "type": ["array", "null"],
                "items": {
                    "type": "object",
                    "properties": {
                        "company_name": {"type": ["string", "null"]},
                        "industry": {"type": ["string", "null"]},
                        "hq_location": {"type": ["string", "null"]},
                        "short_description": {"type": ["string", "null"]}
                    },
                    "additionalProperties": false
                },
                "description": "Array of objects with structured item definitions. Use null if data cannot be found or fulfilled."
            }
        },
        "additionalProperties": false
    }
    
    For text format:
    {
        "format": "text"
    }
    
    Returns:
    {
        "type": "object",
        "properties": {
            "text_response": {
                "type": "string",
                "description": "A text response"
            }
        },
        "required": ["text_response"]
    }
    
    Note: All JSON format fields allow null values for missing data,
    but text_response is always required for text format.
    """
    # Handle text format
    if extraction_schema.get("format") == "text":
        return {
            "type": "object",
            "properties": {
                "text_response": {
                    "type": "string",
                    "description": "A text response"
                }
            },
            "required": ["text_response"]
        }
    
    # Handle json format (existing logic)
    if "fields" not in extraction_schema:
        raise ValueError("Invalid extraction schema: missing 'fields' key. Expected format: {{'format': 'json', 'fields': {{'field_name': 'data_type'}}}} or {{'format': 'text'}}")
    
    fields = extraction_schema["fields"]
    
    # Convert simple types to JSON Schema types with null support
    type_mapping = {
        "string": "string",
        "integer": "integer", 
        "int": "integer",
        "number": "number",
        "float": "number",
        "boolean": "boolean",
        "bool": "boolean",
        "array": "array",
        "object": "object"
    }
    
    def process_field_definition(field_def: Any) -> Dict[str, Any]:
        """Process a field definition, handling both simple types and nested objects."""
        if isinstance(field_def, str):
            # Simple type (existing behavior)
            json_schema_type = type_mapping.get(field_def.lower(), "string")
            return {
                "type": [json_schema_type, "null"],
                "description": f"Field of type {json_schema_type}. Use null if data cannot be found or fulfilled."
            }
        elif isinstance(field_def, dict):
            # Nested object definition (new functionality)
            if field_def.get("type") == "array" and "items" in field_def:
                # Handle nested array with item definitions
                items_schema = field_def["items"]
                
                # Process item schema properties
                item_properties = {}
                for item_field_name, item_field_type in items_schema.items():
                    item_properties[item_field_name] = process_field_definition(item_field_type)
                
                return {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": item_properties,
                        "additionalProperties": False
                    },
                    "description": "Array of objects with structured item definitions. Use null if data cannot be found or fulfilled."
                }
            elif field_def.get("type") in type_mapping:
                # Handle direct type specification like {"type": "string"}
                json_schema_type = type_mapping.get(field_def["type"].lower(), "string")
                return {
                    "type": [json_schema_type, "null"],
                    "description": f"Field of type {json_schema_type}. Use null if data cannot be found or fulfilled."
                }
            else:
                # Handle nested object (not array)
                nested_properties = {}
                for nested_field_name, nested_field_type in field_def.items():
                    if nested_field_name != "type":  # Skip type field for object definitions
                        nested_properties[nested_field_name] = process_field_definition(nested_field_type)
                
                return {
                    "type": ["object", "null"],
                    "properties": nested_properties,
                    "additionalProperties": False,
                    "description": "Nested object. Use null if data cannot be found or fulfilled."
                }
        else:
            # Fallback to string for unknown types
            return {
                "type": ["string", "null"],
                "description": "Field of type string (fallback). Use null if data cannot be found or fulfilled."
            }
    
    properties = {}
    for field_name, field_def in fields.items():
        properties[field_name] = process_field_definition(field_def)
    
    return {
        "type": "object",
        "properties": properties,
        "additionalProperties": False
        # Note: No "required" field - all fields are optional and can be null
    }

