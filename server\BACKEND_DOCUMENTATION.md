# CatchUp Backend Technical Documentation

**Target Audience**: New team members  
**Estimated Reading Time**: 30-45 minutes  
**Last Updated**: 2025-08-01

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [System Components](#system-components)
3. [UML Sequence Diagrams](#uml-sequence-diagrams)
4. [Component Diagrams](#component-diagrams)
5. [API Flow Documentation](#api-flow-documentation)
6. [Data Flow Diagrams](#data-flow-diagrams)
7. [Key Workflows](#key-workflows)
8. [Development Guidelines](#development-guidelines)

---

## Architecture Overview

The CatchUp backend is a sophisticated multi-tenant marketplace customer service system built with **LangGraph** and integrated with **Model Context Protocol (MCP)** tools. The system follows a layered architecture pattern with clear separation of concerns.

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   Web Frontend  │    │  Mobile Apps    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    Authentication Proxy   │
                    │      (server/app.py)      │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     LangGraph Server      │
                    │   (Internal Port 8123)    │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────▼───────┐    ┌─────────▼─────────┐    ┌─────────▼─────────┐
│  CatchUp Agent │    │    MCP Tools      │    │   Supabase DB     │
│ (src/catchup)  │    │ (External Server) │    │   (PostgreSQL)    │
└───────────────┘    └───────────────────┘    └───────────────────┘
```

### Core Principles

- **Multi-tenant Architecture**: Supports multiple businesses and users
- **Microservices Pattern**: Modular components with clear interfaces
- **Event-driven Processing**: Asynchronous tool execution and streaming responses
- **Security-first Design**: API key authentication and CORS protection
- **Scalable Infrastructure**: Proxy pattern for load balancing and monitoring

---

## System Components

### 1. Authentication Proxy Server (`/server`)

The proxy server acts as the entry point and security layer for all requests.

**Key Files:**
- `app.py` - Application factory and middleware assembly
- `config.py` - Centralized configuration management
- `proxy.py` - Request forwarding and streaming support
- `middleware/auth.py` - API key authentication
- `middleware/cors.py` - Cross-origin resource sharing

**Responsibilities:**
- API key validation
- CORS handling
- Request/response proxying
- Health monitoring
- Error handling and logging

### 2. LangGraph Agent System (`/src/catchup`)

The core AI agent that processes customer service interactions.

**Key Files:**
- `graph.py` - Main workflow definition
- `state.py` - State management and message handling
- `nodes/` - Individual processing nodes
- `tools/` - Local tool implementations
- `prompts.py` - System prompts and templates

**Responsibilities:**
- Natural language understanding
- Intent detection and routing
- Tool orchestration
- Response generation
- Conversation memory management

### 3. MCP Tools Integration (`/src/shared`)

External tools for business operations via Model Context Protocol.

**Key Files:**
- `mcp_tools.py` - MCP client and tool loading
- `llm_factory.py` - LLM provider abstraction

**Available Tools:**
- Business operations (bookings, deals, categories)
- Communication (email, WhatsApp)
- Data retrieval (user details, chat history)

---

## UML Sequence Diagrams

### Customer Service Interaction Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant P as Proxy Server
    participant L as LangGraph Server
    participant A as CatchUp Agent
    participant T as Tools
    participant D as Database

    C->>P: POST /threads/{thread_id}/runs
    P->>P: Validate API Key
    P->>L: Forward Request
    L->>A: Initialize Agent State
    A->>A: Enhance Query
    A->>A: Call Model (LLM)
    
    alt Tool Call Required
        A->>T: Execute Tool
        T->>D: Query Database
        D-->>T: Return Data
        T-->>A: Tool Result
        A->>A: Call Model with Results
    end
    
    A-->>L: Final Response
    L-->>P: Stream Response
    P-->>C: Stream to Client
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant AM as Auth Middleware
    participant PM as Proxy Middleware
    participant L as LangGraph Server

    C->>AM: Request with API Key
    AM->>AM: Extract API Key
    
    alt Valid API Key
        AM->>PM: Forward Request
        PM->>L: Proxy to LangGraph
        L-->>PM: Response
        PM-->>AM: Response
        AM-->>C: Success Response
    else Invalid/Missing API Key
        AM-->>C: 401 Unauthorized
    end
```

### Tool Execution Flow

```mermaid
sequenceDiagram
    participant A as Agent
    participant TN as Tools Node
    participant LT as Local Tools
    participant MT as MCP Tools
    participant DB as Database
    participant EXT as External APIs

    A->>TN: Tool Call Request
    TN->>TN: Identify Tool Type
    
    alt Local Tool
        TN->>LT: Execute Local Tool
        LT->>DB: Database Query
        DB-->>LT: Data Response
        LT-->>TN: Tool Result
    else MCP Tool
        TN->>MT: Execute MCP Tool
        MT->>EXT: External API Call
        EXT-->>MT: API Response
        MT-->>TN: Tool Result
    end
    
    TN-->>A: Formatted Result
```

---

## Component Diagrams

### Backend Module Architecture

```mermaid
graph TB
    subgraph "Server Layer"
        APP[app.py<br/>Application Factory]
        CONFIG[config.py<br/>Configuration]
        PROXY[proxy.py<br/>Request Proxy]
        AUTH[middleware/auth.py<br/>Authentication]
        CORS[middleware/cors.py<br/>CORS Handler]
    end

    subgraph "Agent Layer"
        GRAPH[graph.py<br/>Workflow Definition]
        STATE[state.py<br/>State Management]
        NODES[nodes/<br/>Processing Nodes]
        PROMPTS[prompts.py<br/>System Prompts]
    end

    subgraph "Tools Layer"
        TOOLS_LOADER[tools_loader.py<br/>Tool Registry]
        LOCAL_TOOLS[tools/<br/>Local Tools]
        MCP_CLIENT[mcp_tools.py<br/>MCP Client]
    end

    subgraph "Data Layer"
        SUPABASE[supabase/client.py<br/>Database Client]
        MODELS[Models/<br/>Data Models]
    end

    APP --> AUTH
    APP --> CORS
    APP --> PROXY
    PROXY --> GRAPH
    GRAPH --> NODES
    NODES --> TOOLS_LOADER
    TOOLS_LOADER --> LOCAL_TOOLS
    TOOLS_LOADER --> MCP_CLIENT
    LOCAL_TOOLS --> SUPABASE
    SUPABASE --> MODELS
```

### Node Processing Pipeline

```mermaid
graph LR
    START([Start]) --> ENHANCE[enhance_query<br/>Query Enhancement]
    ENHANCE --> CALL_MODEL[call_model<br/>LLM Processing]
    CALL_MODEL --> DECISION{Tool Call<br/>Required?}
    DECISION -->|Yes| TOOLS[tools_node<br/>Tool Execution]
    DECISION -->|No| END([End])
    TOOLS --> CALL_MODEL
```

---

## API Flow Documentation

### Request Processing Pipeline

1. **Client Request** → Authentication Proxy (Port 8000)
2. **Authentication** → API Key validation via middleware
3. **CORS Handling** → Cross-origin request processing
4. **Request Forwarding** → Proxy to LangGraph Server (Port 8123)
5. **Agent Processing** → CatchUp agent workflow execution
6. **Response Streaming** → Real-time response delivery

### Critical Endpoints

#### Health Check Endpoints
- `GET /ok` - Simple health check
- `GET /health` - Basic health status
- `GET /health-detailed` - Comprehensive system status

#### LangGraph API Endpoints
- `POST /threads` - Create new conversation thread
- `POST /threads/{thread_id}/runs` - Execute agent run
- `GET /threads/{thread_id}/runs/{run_id}/stream` - Stream agent responses

### Middleware Stack Processing Order

```
Request Flow: Client → CORS → Auth → Proxy → LangGraph
Response Flow: LangGraph → Proxy → Auth → CORS → Client
```

---

## Data Flow Diagrams

### End-to-End Data Flow

```mermaid
flowchart TD
    USER[User Input] --> PROXY[Authentication Proxy]
    PROXY --> ENHANCE[Query Enhancement]
    ENHANCE --> LLM[LLM Processing]
    LLM --> DECISION{Tool Required?}
    
    DECISION -->|Yes| TOOL_EXEC[Tool Execution]
    TOOL_EXEC --> DB_QUERY[Database Query]
    DB_QUERY --> DB[(Supabase Database)]
    DB --> TOOL_RESULT[Tool Result]
    TOOL_RESULT --> LLM
    
    DECISION -->|No| RESPONSE[Generate Response]
    LLM --> RESPONSE
    RESPONSE --> STREAM[Stream to Client]
    STREAM --> USER
```

### Database Interaction Patterns

```mermaid
erDiagram
    AGENT ||--o{ TOOLS : executes
    TOOLS ||--o{ DATABASE : queries
    DATABASE ||--o{ TABLES : contains
    
    TABLES {
        string user_details
        string categories
        string deals_dealid_view
        string messages
        string booking_with_all_details
    }
```

---

## Key Workflows

### 1. Customer Service Query Processing

1. **Input Reception**: User sends query via API
2. **Authentication**: Proxy validates API key
3. **Query Enhancement**: AI improves query clarity and context
4. **Intent Analysis**: Agent determines user intent and required actions
5. **Tool Selection**: System identifies relevant tools for execution
6. **Data Retrieval**: Tools query database or external services
7. **Response Generation**: LLM synthesizes final response
8. **Streaming Delivery**: Response streamed back to client

### 2. Multi-tenant Data Isolation

- Each request includes `user_id` and `business_id` context
- Database queries automatically filter by tenant
- Tools respect tenant boundaries in data access
- Session management maintains tenant context

### 3. Error Handling and Recovery

- Graceful degradation for tool failures
- Automatic retry mechanisms for transient errors
- Comprehensive logging for debugging
- User-friendly error messages

---

## Development Guidelines

### Adding New Tools

1. Create tool implementation in `src/catchup/tools/`
2. Add tool to `tools_loader.py`
3. Update tool registry in `__init__.py`
4. Add appropriate error handling
5. Include streaming notifications

### Extending the Agent

1. Define new nodes in `src/catchup/nodes/`
2. Update graph definition in `graph.py`
3. Modify state schema if needed
4. Add routing logic for new workflows

### Configuration Management

- Use environment variables for all configuration
- Validate configuration at startup
- Provide clear error messages for missing config
- Support different environments (dev/staging/prod)

### Testing Strategy

- Unit tests for individual components
- Integration tests for end-to-end workflows
- Load testing for performance validation
- Security testing for authentication flows

---

## Code Examples and Implementation Details

### 1. Creating a New Local Tool

Here's how to implement a new local tool for the CatchUp system:

```python
# src/catchup/tools/get_example_data.py
from typing import Optional, List, Tuple
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from catchup.supabase.client import supabase
from catchup.Models.model import ExampleModel

@tool
def get_example_data(
    user_id: str,
    filter_param: Optional[str] = None
) -> Tuple[Optional[List[ExampleModel]], Optional[str]]:
    """Fetch example data with optional filtering.

    Args:
        user_id: The ID of the user requesting data
        filter_param: Optional filter parameter

    Returns:
        Tuple containing (data_list, error_message)
    """
    try:
        # Stream notification for tool execution
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"invoking get_example_data for {user_id}"})

        # Build query with user context
        query = supabase.table('example_table').select('*').eq('user_id', user_id)

        if filter_param:
            query = query.eq('filter_field', filter_param)

        response = query.execute()

        if response.data:
            # Map to model objects
            results = [ExampleModel(**item) for item in response.data]
            return results, None
        else:
            return [], None

    except Exception as e:
        return None, str(e)
```

### 2. Adding a New Processing Node

```python
# src/catchup/nodes/example_node.py
from typing import Any, Dict
from langchain_core.runnables import RunnableConfig
from langgraph.types import StreamWriter
from catchup.state import CatchUpState

async def example_processing_node(
    state: CatchUpState,
    writer: StreamWriter,
    config: RunnableConfig
) -> Dict[str, Any]:
    """Example processing node implementation.

    Args:
        state: Current conversation state
        writer: Stream writer for real-time updates
        config: Node configuration

    Returns:
        State updates dictionary
    """
    # Extract relevant data from state
    messages = state.get("messages", [])
    user_id = state.get("user_id")

    # Perform processing logic
    processed_data = await process_user_request(messages, user_id)

    # Stream progress update
    writer({"processing_status": "completed", "data": processed_data})

    # Return state updates
    return {
        "processed_data": processed_data,
        "processing_complete": True
    }
```

### 3. Configuration Management Example

```python
# Environment variable configuration
ROCKET_API_KEY=your_api_key_here
LANGGRAPH_INTERNAL_PORT=8123
PORT=8000
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
DATABASE_URI=postgresql://user:pass@host:port/db
LANGSMITH_API_KEY=your_langsmith_key
LANGSMITH_TRACING=true
LOG_LEVEL=INFO
ENVIRONMENT=production
```

### 4. Middleware Stack Configuration

```python
# server/app.py - Middleware assembly order
def create_proxy_app(config: ServerConfig) -> Starlette:
    app = Starlette()

    # Order matters: last added runs first
    # 1. Proxy (runs last - forwards to LangGraph)
    app.add_middleware(LangGraphProxyMiddleware, langgraph_url=config.langgraph_url)

    # 2. Authentication (runs second - validates API keys)
    app.add_middleware(APIKeyAuthMiddleware, config=config)

    # 3. CORS (runs first - handles preflight requests)
    add_cors_middleware(app, config)

    return app
```

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Tool Execution Failures
**Symptom**: Tools return errors or timeout
**Solutions**:
- Check database connectivity in `supabase/client.py`
- Verify MCP server is running and accessible
- Review tool parameter validation
- Check streaming writer context availability

#### 2. Authentication Issues
**Symptom**: 401 Unauthorized responses
**Solutions**:
- Verify `ROCKET_API_KEY` environment variable
- Check API key format in request headers (`x-api-key`)
- Ensure internal paths are properly excluded from auth
- Review CORS configuration for preflight requests

#### 3. Memory and Performance Issues
**Symptom**: High memory usage or slow responses
**Solutions**:
- Adjust `memory_length` parameter in state configuration
- Review message filtering in `enhanced_memory_reducer`
- Monitor tool execution timeouts
- Check database query optimization

#### 4. Streaming Response Problems
**Symptom**: Incomplete or broken streaming responses
**Solutions**:
- Verify `StreamWriter` usage in nodes and tools
- Check proxy streaming configuration
- Review client-side streaming implementation
- Monitor network connectivity and timeouts

---

## Performance Optimization

### 1. Database Query Optimization
- Use appropriate indexes on frequently queried columns
- Implement query result caching where appropriate
- Optimize JOIN operations in database views
- Monitor query execution times

### 2. Memory Management
- Configure appropriate message history limits
- Implement efficient state serialization
- Use lazy loading for large datasets
- Monitor memory usage patterns

### 3. Tool Execution Optimization
- Implement parallel tool execution where possible
- Cache frequently accessed data
- Optimize external API calls
- Use connection pooling for database access

---

## Security Considerations

### 1. API Security
- Always validate API keys before processing requests
- Use HTTPS in production environments
- Implement rate limiting for API endpoints
- Log security events for monitoring

### 2. Data Protection
- Ensure tenant data isolation in all queries
- Validate user permissions before data access
- Sanitize user inputs to prevent injection attacks
- Encrypt sensitive data in transit and at rest

### 3. Environment Security
- Store secrets in environment variables, not code
- Use different API keys for different environments
- Regularly rotate API keys and credentials
- Monitor for unauthorized access attempts

---

## Monitoring and Observability

### 1. Logging Strategy
- Use structured logging with appropriate levels
- Include correlation IDs for request tracing
- Log tool execution times and results
- Monitor error rates and patterns

### 2. Health Monitoring
- Implement comprehensive health checks
- Monitor database connectivity
- Track LangGraph server status
- Set up alerting for critical failures

### 3. Performance Metrics
- Track response times for different endpoints
- Monitor tool execution performance
- Measure memory and CPU usage
- Analyze user interaction patterns

---

## Deployment and Operations

### 1. Local Development Setup

```bash
# 1. Clone repository and install dependencies
git clone <repository-url>
cd LanCatchUp
pip install -e .

# 2. Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# 3. Start MCP server (if using external MCP tools)
# Follow MCP server setup instructions

# 4. Start the backend services
python server/server_proxy.py
```

### 2. Production Deployment

```bash
# 1. Environment configuration
export ENVIRONMENT=production
export ROCKET_API_KEY=<secure-api-key>
export DATABASE_URI=<production-database-url>
export LANGSMITH_API_KEY=<langsmith-key>
export CORS_ALLOWED_ORIGINS=<production-domains>

# 2. Start services with process manager
uvicorn server.app:create_proxy_app --host 0.0.0.0 --port 8000 --workers 4

# 3. Monitor health endpoints
curl http://localhost:8000/health-detailed
```

### 3. Docker Deployment

```dockerfile
# Dockerfile example
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "server/server_proxy.py"]
```

### 4. Environment-Specific Configuration

| Environment | Port | Auth Required | Logging Level | CORS |
|-------------|------|---------------|---------------|------|
| Development | 8000 | Optional | DEBUG | Permissive |
| Staging | 8000 | Required | INFO | Restricted |
| Production | 8000 | Required | WARNING | Strict |

---

## API Reference Quick Guide

### Authentication
```bash
# Header-based authentication (preferred)
curl -H "x-api-key: your-api-key" http://localhost:8000/threads

# Query parameter authentication (fallback)
curl "http://localhost:8000/threads?api-key=your-api-key"
```

### Core Endpoints
```bash
# Health checks
GET /ok                    # Simple health check
GET /health               # Basic health status
GET /health-detailed      # Comprehensive system status

# LangGraph API (proxied)
POST /threads             # Create conversation thread
POST /threads/{id}/runs   # Execute agent run
GET /threads/{id}/runs/{run_id}/stream  # Stream responses
```

### Response Formats
```json
{
  "llmResponse": "AI-generated response text",
  "llmResponseIntent": "detected_intent_category",
  "userIntent": "user_request_classification",
  "relatedIds": {
    "businessId": "uuid",
    "conversationId": "uuid",
    "dealIds": ["uuid1", "uuid2"]
  }
}
```

---

## Team Onboarding Checklist

### For New Developers

- [ ] **Environment Setup**
  - [ ] Clone repository and install dependencies
  - [ ] Configure local environment variables
  - [ ] Verify database connectivity
  - [ ] Test MCP server connection

- [ ] **Architecture Understanding**
  - [ ] Review this documentation thoroughly
  - [ ] Understand the proxy → LangGraph → agent flow
  - [ ] Familiarize with the middleware stack
  - [ ] Study the tool execution patterns

- [ ] **Code Exploration**
  - [ ] Examine the main graph definition (`src/catchup/graph.py`)
  - [ ] Review tool implementations (`src/catchup/tools/`)
  - [ ] Understand state management (`src/catchup/state.py`)
  - [ ] Study the proxy server (`server/`)

- [ ] **Testing and Validation**
  - [ ] Run unit tests: `pytest tests/unit_tests/`
  - [ ] Execute integration tests: `pytest tests/integration_tests/`
  - [ ] Test local development setup
  - [ ] Verify health endpoints respond correctly

- [ ] **Development Workflow**
  - [ ] Understand branching strategy
  - [ ] Review code review process
  - [ ] Learn deployment procedures
  - [ ] Set up monitoring and logging access

### For DevOps/Infrastructure

- [ ] **Infrastructure Setup**
  - [ ] Configure production environment
  - [ ] Set up monitoring and alerting
  - [ ] Implement backup strategies
  - [ ] Configure load balancing

- [ ] **Security Configuration**
  - [ ] Secure API key management
  - [ ] Configure HTTPS/TLS
  - [ ] Set up firewall rules
  - [ ] Implement access controls

- [ ] **Operational Procedures**
  - [ ] Document deployment process
  - [ ] Create runbooks for common issues
  - [ ] Set up log aggregation
  - [ ] Configure performance monitoring

---

## Glossary

**Agent**: The LangGraph-based AI system that processes customer service requests
**MCP**: Model Context Protocol - standard for connecting AI models to external tools
**Node**: Individual processing unit in the LangGraph workflow
**Proxy**: Authentication and routing layer that sits in front of LangGraph
**State**: Conversation context and data passed between workflow nodes
**Stream Writer**: Interface for real-time progress updates during processing
**Tool**: External function or API that the agent can call to perform actions
**Thread**: Conversation session with persistent state and memory

---

## Additional Resources

- **LangGraph Documentation**: https://langchain-ai.github.io/langgraphjs/
- **Model Context Protocol**: https://modelcontextprotocol.io/
- **Supabase Documentation**: https://supabase.com/docs
- **FastAPI/Starlette Docs**: https://www.starlette.io/
- **Project Repository**: [Internal Git Repository]

---

*This comprehensive documentation covers all aspects of the CatchUp backend system. For questions or clarifications, please reach out to the development team or refer to the inline code documentation.*

**Document Version**: 1.0
**Last Updated**: 2025-08-01
**Next Review**: 2025-09-01
