# CatchUp LangGraph Customer Service System

A sophisticated multi-tenant marketplace customer service system built with [LangGraph](https://github.com/langchain-ai/langgraph) and integrated with Model Context Protocol (MCP) tools.

## Overview

This system provides AI-powered customer service for a marketplace platform, featuring:

- **Multi-tenant Architecture**: Supports multiple businesses and users
- **MCP Tool Integration**: 10+ specialized tools for categories, deals, bookings, and communication
- **Robust Error Handling**: Custom solutions for LLM tool calling reliability
- **Memory Management**: Configurable conversation history with session persistence
- **Multi-modal Communication**: Email and WhatsApp integration

## Key Features

- **Category Management**: Dynamic category retrieval and search
- **Deal Discovery**: Intelligent deal searching with location and preference awareness
- **Booking System**: Complete booking lifecycle management
- **User Management**: Comprehensive user profile and history tracking
- **Communication Tools**: Professional email templates and WhatsApp messaging
- **Error Recovery**: Advanced error handling for invalid tool calls and system resilience

## Architecture

The system is built around a LangGraph workflow that orchestrates:

1. **Call Model Node**: LLM processing with bound MCP tools
2. **Tools Node**: Custom tool execution with error handling
3. **Conditional Routing**: Intelligent flow control between model and tools
4. **Memory Management**: Session-based conversation persistence

## Technical Challenges Solved

This project overcame several critical technical challenges in LLM tool integration:

- **Invalid Tool Call Handling**: Automatic correction of malformed tool calls
- **Async Function Integration**: Proper LangGraph node function signatures
- **Error Recovery**: Graceful degradation and conversation flow preservation

For detailed technical documentation, see:
- [Technical Challenges and Solutions](docs/TECHNICAL_CHALLENGES.md)
- [MCP Integration Guide](docs/MCP_INTEGRATION.md)
- [Memory Functionality](docs/MEMORY_FUNCTIONALITY.md)

## Available Tools

The system integrates with 10+ MCP tools:

- `get_categories` - Retrieve all available service categories
- `search_deals` - Find deals and offers by category and location
- `get_user_details` - Access user profile information
- `get_chat_history` - Retrieve conversation history
- `get_deals` - Get business-specific deals and offers
- `get_business_details` - Access company information
- `get_booking_details` - Retrieve existing booking information
- `create_booking` - Create new bookings for deals
- `sent_email_to_users` - Send professional HTML emails
- `whatsapp_sent_tool` - Send WhatsApp messages

## Getting Started

### Prerequisites

- Python 3.8+
- Access to OpenRouter API (or other LLM provider)
- MCP server running at `https://genenrativepangea.app.n8n.cloud/mcp/catchup/sse`

### Installation

1. Install dependencies:

```bash
pip install -e . "langgraph-cli[inmem]"
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Add your API keys to the `.env` file:

```text
# .env
OPENROUTER_API_KEY=your_openrouter_key_here
LANGSMITH_API_KEY=lsv2...  # Optional for tracing
```

3. Test the system:

```bash
python test_langgraph_simple.py
```

4. Start the LangGraph Server:

```shell
langgraph dev
```
# Starts
uvx --from "langgraph-cli[inmem]" --with-editable . langgraph dev   
### Quick Test

Test the system with a simple query:

```python
from langchain_core.messages import HumanMessage
from agent.graph import graph

# Test aperitivo search
inputs = {
    "messages": [HumanMessage(content="Cerco per i prossimi giorni, un aperitivo")],
    "user_id": "test_user_123",
    "latitude": "45.4666",
    "longitude": "9.1832"
}

config = {
    "configurable": {
        "model_name": "anthropic/claude-3.5-sonnet",
        "system_prompt": "You are a helpful customer service assistant."
    }
}

result = await graph.ainvoke(inputs, config)
```

Expected output: The system will find aperitivo deals and return structured information with pricing, availability, and booking details.

## Docker
run  docker compose up --build  

## Development Insights

### Key Learnings

This project demonstrates several important patterns for production LangGraph applications:

1. **Robust Tool Integration**: Custom tool nodes that handle both valid and invalid tool calls
2. **Error Recovery**: Systems that gracefully handle LLM inconsistencies
3. **Async Patterns**: Proper integration of async operations in LangGraph workflows
4. **Memory Management**: Configurable conversation history with session persistence

### Common Pitfalls Avoided

- **Tool Call Reliability**: LLMs may generate invalid tool calls even with clear schemas
- **Function Signatures**: LangGraph nodes require specific `(state, config)` signatures
- **Error Handling**: Invalid tool calls should be processed, not ignored
- **State Management**: Proper handling of conversation state and memory limits

### Testing Strategy

The system includes comprehensive testing:

```bash
# Test MCP connection and tools
python test_mcp_connection.py

# Test complete workflow
python test_langgraph_simple.py

# Run integration tests
pytest tests/integration_tests/ -v
```

## Customization

### Adding New Tools

1. Add tools to the MCP server - they'll be automatically available
2. Update system prompts to describe new tool capabilities
3. Test tool integration with the custom error handling

### Modifying Conversation Flow

1. **Update the graph structure** in `src/agent/graph.py`
2. **Modify conditional edges** for custom routing logic
3. **Extend the state schema** in `src/agent/state.py` for new data

### Custom Error Handling

Extend the `CustomToolNode` class in `src/agent/nodes/tools_node.py` to handle specific tool errors:

```python
# Handle specific tool errors
if tool_name == 'your_custom_tool' and 'specific_error' in error:
    # Custom error handling logic
    corrected_result = await handle_custom_error(invalid_call)
    return {"messages": [ToolMessage(content=corrected_result, tool_call_id=tool_id)]}
```

## Production Considerations

- **Monitoring**: Implement logging for invalid tool calls and error patterns
- **Rate Limiting**: Consider API rate limits for both LLM and MCP server
- **Caching**: Cache frequently accessed data like categories and user details
- **Security**: Validate all tool inputs and sanitize outputs
- **Scalability**: Consider connection pooling for high-traffic scenarios

For detailed technical documentation and troubleshooting, see the [docs](docs/) directory.

<!--
Configuration auto-generated by `langgraph template lock`. DO NOT EDIT MANUALLY.
{
  "config_schemas": {
    "agent": {
      "type": "object",
      "properties": {}
    }
  }
}
-->
