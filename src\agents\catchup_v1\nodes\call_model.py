"""Call model node for CatchUp v1 agent."""

from __future__ import annotations

from typing import Dict, Any, List
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate

from agents.catchup_v1.state import CatchUpV1State
from agents.catchup_v1.configuration import CatchUpV1Configuration
from agents.catchup_v1.tools.marketplace import get_enhanced_marketplace_tools
from agents.catchup_v1.tools.planning import create_plan, update_task_status, get_current_plan
from agents.catchup_v1.tools.communication import send_progress_update, send_plan_summary, stream_status_update
from shared.llm_factory import create_llm


async def call_model_node(state: CatchUpV1State, config: RunnableConfig) -> Dict[str, Any]:
    """Call model node that handles LLM interactions with tool binding.
    
    This node:
    1. Gets the configured LLM
    2. Binds all available tools to the model
    3. Creates context-aware system prompts
    4. Handles the conversation with tool calling support
    5. Returns the model's response
    """
    
    configuration = CatchUpV1Configuration.from_runnable_config(config)
    messages = state.get("messages", [])
    user_context = state.get("user_context", {})
    session_config = state.get("session_config", {})
    conversation_phase = state.get("conversation_phase", {})
    current_plan = state.get("current_plan", [])
    progress_metrics = state.get("progress_metrics", {})
    
    # Get LLM
    llm = create_llm(configuration.model_name, temperature=configuration.model_temperature)
    
    # Get all available tools
    marketplace_tools = await get_enhanced_marketplace_tools()
    planning_tools = [create_plan, update_task_status, get_current_plan]
    communication_tools = [send_progress_update, send_plan_summary, stream_status_update]
    
    all_tools = marketplace_tools + planning_tools + communication_tools
    
    # Bind tools to the model (following the preferred pattern)
    llm_with_tools = llm.bind_tools(all_tools)
    
    # Create context-aware system prompt
    system_prompt = _create_context_aware_system_prompt(
        configuration,
        user_context,
        session_config,
        conversation_phase,
        current_plan,
        progress_metrics
    )
    
    # Create the prompt template
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("placeholder", "{messages}")
    ])
    
    # Create the chain
    chain = prompt | llm_with_tools
    
    try:
        # Invoke the model
        response = await chain.ainvoke({"messages": messages})
        
        # Update conversation context if needed
        conversation_context = state.get("conversation_context", {})
        updated_context = {
            **conversation_context,
            "last_model_call": {
                "timestamp": "datetime.now().isoformat()",
                "model": configuration.model_name,
                "tools_available": len(all_tools),
                "has_tool_calls": bool(getattr(response, 'tool_calls', None))
            }
        }
        
        return {
            "messages": [response],
            "conversation_context": updated_context
        }
        
    except Exception as e:
        error_message = f"I encountered an error while processing your request: {str(e)}"
        
        return {
            "messages": [AIMessage(content=error_message)],
            "error_context": {
                "node": "call_model",
                "error": str(e),
                "model": configuration.model_name,
                "tools_count": len(all_tools)
            }
        }


def _create_context_aware_system_prompt(
    configuration: CatchUpV1Configuration,
    user_context: Dict[str, Any],
    session_config: Dict[str, Any],
    conversation_phase: Dict[str, Any],
    current_plan: List[Dict[str, Any]],
    progress_metrics: Dict[str, Any]
) -> str:
    """Create a context-aware system prompt based on current state."""
    
    base_prompt = configuration.system_prompt
    
    # Add user context
    context_parts = [base_prompt]
    
    # User information
    if user_context:
        context_parts.append("\n--- USER CONTEXT ---")
        context_parts.append(f"User ID: {user_context.get('user_id', 'Unknown')}")
        
        if user_context.get('email_address'):
            context_parts.append(f"Email: {user_context['email_address']}")
        
        if user_context.get('location'):
            location = user_context['location']
            context_parts.append(f"Location: {location.get('latitude', 'N/A')}, {location.get('longitude', 'N/A')}")
        
        if user_context.get('preferences'):
            context_parts.append(f"Preferences: {user_context['preferences']}")
    
    # Session information
    if session_config:
        context_parts.append("\n--- SESSION CONFIG ---")
        context_parts.append(f"Session ID: {session_config.get('session_id', 'Unknown')}")
        context_parts.append(f"Memory Length: {session_config.get('memory_length', 15)} messages")
        context_parts.append(f"Communication Enabled: {session_config.get('communication_enabled', True)}")
        context_parts.append(f"Streaming Enabled: {session_config.get('streaming_enabled', True)}")
    
    # Conversation phase
    if conversation_phase:
        phase = conversation_phase.get('phase', 'unknown')
        confidence = conversation_phase.get('confidence', 0.0)
        context_parts.append(f"\n--- CURRENT PHASE ---")
        context_parts.append(f"Phase: {phase} (confidence: {confidence:.1f})")
        
        if phase == "planning":
            context_parts.append("Focus on creating comprehensive plans and breaking down complex requests.")
        elif phase == "executing":
            context_parts.append("Focus on executing tasks efficiently and providing progress updates.")
        elif phase == "communicating":
            context_parts.append("Focus on sending updates and communicating with the user.")
        elif phase == "closing":
            context_parts.append("Focus on summarizing results and closing the conversation professionally.")
    
    # Current plan status
    if current_plan:
        total_tasks = len(current_plan)
        completed_tasks = progress_metrics.get('completed_tasks', 0)
        failed_tasks = progress_metrics.get('failed_tasks', 0)
        
        context_parts.append(f"\n--- CURRENT PLAN STATUS ---")
        context_parts.append(f"Total Tasks: {total_tasks}")
        context_parts.append(f"Completed: {completed_tasks}")
        context_parts.append(f"Failed: {failed_tasks}")
        context_parts.append(f"Remaining: {total_tasks - completed_tasks - failed_tasks}")
        
        # Show active task
        pending_tasks = [task for task in current_plan if task["status"] == "pending"]
        in_progress_tasks = [task for task in current_plan if task["status"] == "in_progress"]
        
        if in_progress_tasks:
            active_task = in_progress_tasks[0]
            context_parts.append(f"Active Task: {active_task['content']}")
        elif pending_tasks:
            next_task = pending_tasks[0]
            context_parts.append(f"Next Task: {next_task['content']}")
    
    # Behavioral guidelines based on context
    context_parts.append("\n--- BEHAVIORAL GUIDELINES ---")
    
    if conversation_phase.get('phase') == 'planning':
        context_parts.append("- Create detailed, actionable plans for complex requests")
        context_parts.append("- Break down tasks into atomic, executable units")
        context_parts.append("- Consider tool availability and user context")
    
    if current_plan:
        context_parts.append("- Use update_task_status to track progress")
        context_parts.append("- Send progress updates for long-running operations")
        context_parts.append("- Handle task failures gracefully with error recovery")
    
    if session_config.get('communication_enabled'):
        context_parts.append("- Proactively communicate progress and results")
        context_parts.append("- Use appropriate communication channels (email/WhatsApp)")
        context_parts.append("- Keep users informed during long operations")
    
    if user_context.get('location'):
        context_parts.append("- Use location information for relevant searches and recommendations")
        context_parts.append("- Filter results based on geographic proximity when appropriate")
    
    # Language preference
    context_parts.append("- Respond in Italian unless the user specifically requests another language")
    context_parts.append("- Be professional, helpful, and proactive in solving customer needs")
    
    return "\n".join(context_parts)
