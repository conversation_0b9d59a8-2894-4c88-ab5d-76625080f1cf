"""Utility functions used in our graph."""

import re
from typing import Any, Dict, List, Optional

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import AnyMessage
from langchain_core.runnables import RunnableConfig

from outbond2.configuration import Configuration




def get_message_text(msg: AnyMessage) -> str:
    """Get the text content of a message."""
    content = msg.content
    if isinstance(content, str):
        return content
    elif isinstance(content, dict):
        return content.get("text", "")
    else:
        txts = [c if isinstance(c, str) else (c.get("text") or "") for c in content]
        return "".join(txts).strip()


def extract_title_from_html(html_content: str) -> str:
    """Extract title from HTML content.
    
    Attempts to extract the page title from HTML content by looking for:
    1. <title> tag content (preferred)
    2. <h1> tag content (fallback)
    3. Default "Web Page" if neither found
    
    Args:
        html_content: Raw HTML content as string
        
    Returns:
        Extracted title string, limited to 100 characters
    """
    try:
        # Look for <title> tag
        title_match = re.search(r'<title[^>]*>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
        if title_match:
            title = title_match.group(1).strip()
            # Clean up HTML entities and extra whitespace
            title = re.sub(r'\s+', ' ', title)
            return title[:100]  # Limit title length
        
        # Fallback: look for h1 tag
        h1_match = re.search(r'<h1[^>]*>(.*?)</h1>', html_content, re.IGNORECASE | re.DOTALL)
        if h1_match:
            title = h1_match.group(1).strip()
            title = re.sub(r'<[^>]+>', '', title)  # Remove any HTML tags
            title = re.sub(r'\s+', ' ', title)
            return title[:100]
            
        return "Web Page"
        
    except Exception:
        return "Web Page"

def init_model(config: Optional[RunnableConfig] = None) -> BaseChatModel:
    """Initialize the configured chat model."""
    configuration = Configuration.from_runnable_config(config)
    fully_specified_name = configuration.model
    if "/" in fully_specified_name:
        provider, model = fully_specified_name.split("/", maxsplit=1)
    else:
        provider = None
        model = fully_specified_name
    return init_chat_model(model, model_provider=provider)


# Citation formatting utilities

def deduplicate_citations(citations: List[Any]) -> List[Any]:
    """Remove duplicate citations based on URL while preserving order.
    
    Args:
        citations: List of Citation objects
        
    Returns:
        List of unique Citation objects (first occurrence preserved)
    """
    seen_urls = set()
    unique_citations = []
    
    for citation in citations:
        if citation.url not in seen_urls:
            seen_urls.add(citation.url)
            unique_citations.append(citation)
    
    return unique_citations


def format_citations_for_text(citations: List[Any]) -> str:
    """Format citations for human-readable text output.
    
    Args:
        citations: List of Citation objects
        
    Returns:
        Formatted citation string for text output
    """
    if not citations:
        return ""
    
    # Deduplicate citations
    unique_citations = deduplicate_citations(citations)
    
    # Format as numbered list
    formatted_citations = []
    for i, citation in enumerate(unique_citations, 1):
        title = citation.title or "Source"
        citation_text = f"[{i}] {title}"
        
        # Add URL
        if citation.url:
            citation_text += f" - {citation.url}"
            
        # Add access date/time if available
        if hasattr(citation, 'access_date') and citation.access_date:
            citation_text += f" (accessed {citation.access_date})"
            
        formatted_citations.append(citation_text)
    
    return "\n\nSources:\n" + "\n".join(formatted_citations)


def format_citations_for_json(citations: List[Any]) -> List[Dict[str, Any]]:
    """Format citations for structured JSON output.
    
    Args:
        citations: List of Citation objects
        
    Returns:
        List of citation dictionaries for JSON output
    """
    if not citations:
        return []
    
    # Deduplicate citations
    unique_citations = deduplicate_citations(citations)
    
    # Convert to dictionaries
    formatted_citations = []
    for citation in unique_citations:
        citation_dict = {
            "url": citation.url,
            "title": citation.title or "Source",
            "access_date": getattr(citation, 'access_date', ''),
            "source_type": getattr(citation, 'source_type', 'web')
        }
        
        # Only include content_snippet if it exists and is not empty
        if hasattr(citation, 'content_snippet') and citation.content_snippet:
            citation_dict["content_snippet"] = citation.content_snippet
            
        formatted_citations.append(citation_dict)
    
    return formatted_citations


def add_citations_to_response(response: Any, citations: List[Any], is_text_format: bool = False) -> Any:
    """Add citations to response based on output format.
    
    Args:
        response: The response content (dict for JSON, str for text)
        citations: List of Citation objects
        is_text_format: Whether this is a text format response
        
    Returns:
        Response with citations added
    """
    if not citations:
        return response
    
    if is_text_format:
        # For text format, append citations as text
        if isinstance(response, dict) and "text_response" in response:
            # Extract text from structured response
            text_content = response["text_response"]
            citations_text = format_citations_for_text(citations)
            response["text_response"] = text_content + citations_text
        elif isinstance(response, str):
            # Direct string response
            citations_text = format_citations_for_text(citations)
            response = response + citations_text
        
        return response
    else:
        # For JSON format, add citations as structured data
        if isinstance(response, dict):
            response_copy = response.copy()
            response_copy["citations"] = format_citations_for_json(citations)
            return response_copy
        else:
            # Fallback: convert to dict with citations
            return {
                "response": response,
                "citations": format_citations_for_json(citations)
            }


def get_citation_summary(citations: List[Any]) -> str:
    """Get a brief summary of citations for logging/debugging.
    
    Args:
        citations: List of Citation objects
        
    Returns:
        Brief summary string
    """
    if not citations:
        return "No citations"
    
    unique_citations = deduplicate_citations(citations)

    # Count by source type
    source_counts: dict[str, int] = {}
    for citation in unique_citations:
        source_type = getattr(citation, 'source_type', 'unknown')
        source_counts[source_type] = source_counts.get(source_type, 0) + 1
    # Format summary
    total = len(unique_citations)
    summary_parts = [f"{total} sources"]
    
    if source_counts:
        type_summaries = [f"{count} {source_type}" for source_type, count in source_counts.items()]
        summary_parts.append(f"({', '.join(type_summaries)})")
    
    return " ".join(summary_parts)
