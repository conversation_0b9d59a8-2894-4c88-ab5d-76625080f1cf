
from outbond.state import SearchPhase, SearchState


def complete_node(state: SearchState) -> SearchState:
    """Complete node that waits for 5 seconds as a dummy operation.
    
    Args:
        state: The current SearchState
        
    Returns:
        Updated SearchState with phase set to COMPLETE after waiting
    """
    print(state.finalAnswer)
        
        # Update state to indicate completion
    return SearchState(
      
        finalAnswer = state.finalAnswer,
        followUpQuestions = state.followUpQuestions,
        sources=state.sources,
        processedSources=state.processedSources,
        
        phase=SearchPhase.COMPLETE,  # Move to analyzing phase
    )
        
   
