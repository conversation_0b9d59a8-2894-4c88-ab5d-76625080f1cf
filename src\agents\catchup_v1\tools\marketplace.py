"""Enhanced marketplace tools for CatchUp v1 agent."""

from __future__ import annotations

from typing import List, Optional, Dict, Any, Annotated
from langchain_core.tools import BaseTool, tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from agents.catchup_v1.state import CatchUpV1State
from shared.mcp_tools import get_catchup_tools_by_names
from catchup.tools import (
    get_user_details_by_id,
    get_all_categories,
    get_deals_by_categoryId,
    search_deals,
    get_deals,
    get_chat_history,
    get_booking_details
)


# Enhanced MCP tools list with better organization
CORE_MCP_TOOLS = [
    "get_business_details",
    "create_booking", 
    "sent_email_to_users",
    "whatsapps_sent_tool"
]

EXTENDED_MCP_TOOLS = [
    "get_categories",
    "search_deals_advanced",
    "get_user_profile",
    "update_booking_status"
]


async def get_enhanced_marketplace_tools() -> List[BaseTool]:
    """Get all enhanced marketplace tools with better error handling and integration.
    
    Returns:
        List of enhanced marketplace tools for CatchUp v1
    """
    
    # Get MCP tools
    try:
        mcp_tools = await get_catchup_tools_by_names(CORE_MCP_TOOLS)
    except Exception as e:
        print(f"Warning: Failed to load MCP tools: {e}")
        mcp_tools = []
    
    # Local tools with enhanced capabilities
    local_tools = [
        get_user_details_by_id,
        get_all_categories,
        get_deals_by_categoryId,
        search_deals,
        get_deals,
        get_chat_history,
        get_booking_details,
        # Enhanced tools
        enhanced_search_deals,
        get_user_context_info,
        validate_booking_request,
        format_deal_response
    ]
    
    all_tools = mcp_tools + local_tools
    
    print(f"Loaded {len(all_tools)} enhanced marketplace tools")
    print(f"MCP tools: {[tool.name for tool in mcp_tools]}")
    print(f"Local tools: {[tool.name for tool in local_tools]}")
    
    return all_tools


@tool
def enhanced_search_deals(
    query: str,
    state: Annotated[Dict[str, Any], InjectedState],  # Change from CatchUpV1State to Dict
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Enhanced search for deals with context awareness and learning capabilities.
    
    Args:
        query: Search query for deals
        state: Current conversation state
        tool_call_id: Tool call identifier
    
    Returns:
        Command with search results and updated state
    """
    
    user_context = state.get("user_context", {})
    
    # Build search context
    search_context = {
        "query": query,
        "user_id": user_context.get("user_id"),
        "location": user_context.get("location") if location_filter else None,
        "category_id": category_id,
        "max_results": max_results
    }
    
    try:
        # Use the original search_deals function but enhance the results
        # This would typically call the actual search function
        # For now, we'll create a placeholder response
        
        search_results = {
            "query": query,
            "results_count": 0,
            "deals": [],
            "search_context": search_context,
            "suggestions": []
        }
        
        # Update tool execution history
        execution_entry = {
            "tool_name": "enhanced_search_deals",
            "executed_at": "datetime.now().isoformat()",
            "parameters": search_context,
            "success": True,
            "results_count": search_results["results_count"]
        }
        
        current_history = state.get("tool_execution_history", [])
        updated_history = current_history + [execution_entry]
        
        return Command(
            update={
                "tool_execution_history": updated_history,
                "conversation_context": {
                    "last_search": search_context,
                    "last_search_results": search_results
                },
                "messages": [
                    ToolMessage(
                        content=f"Enhanced search completed for '{query}'. Found {search_results['results_count']} deals.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )
        
    except Exception as e:
        return Command(
            update={
                "error_context": {
                    "tool_name": "enhanced_search_deals",
                    "error": str(e),
                    "parameters": search_context
                },
                "messages": [
                    ToolMessage(
                        content=f"Error in enhanced search: {str(e)}",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )


@tool
def get_user_context_info(
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    include_history: bool = False,
    include_preferences: bool = True,

) -> Command:
    """Get comprehensive user context information for personalized responses.
    
    Args:
        include_history: Whether to include interaction history
        include_preferences: Whether to include user preferences
    
    Returns:
        Command with user context information
    """
    
    user_context = state.get("user_context", {})
    session_config = state.get("session_config", {})
    
    context_info = {
        "user_id": user_context.get("user_id"),
        "email": user_context.get("email_address"),
        "location": user_context.get("location"),
        "session_id": session_config.get("session_id"),
        "session_start": user_context.get("current_session_start")
    }
    
    if include_preferences:
        context_info["preferences"] = user_context.get("preferences", {})
        context_info["communication_preferences"] = user_context.get("communication_preferences", {})
    
    if include_history:
        context_info["interaction_history"] = user_context.get("interaction_history", [])
        context_info["tool_history"] = state.get("tool_execution_history", [])
    
    return Command(
        update={
            "messages": [
                ToolMessage(
                    content=f"User context retrieved for user {context_info['user_id']}",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def validate_booking_request(
    deal_id: str,
    user_requirements: Dict[str, Any],
    state: Annotated[Dict[str, Any], InjectedState],  # Change from CatchUpV1State to Dict
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Validate a booking request before processing.
    
    Args:
        deal_id: ID of the deal to book
        user_requirements: User's booking requirements
    
    Returns:
        Command with validation results
    """
    
    user_context = state.get("user_context", {})
    
    validation_result = {
        "valid": True,
        "errors": [],
        "warnings": [],
        "deal_id": deal_id,
        "user_id": user_context.get("user_id")
    }
    
    # Basic validation checks
    if not deal_id:
        validation_result["valid"] = False
        validation_result["errors"].append("Deal ID is required")
    
    if not user_context.get("user_id"):
        validation_result["valid"] = False
        validation_result["errors"].append("User ID is required")
    
    # Check user location if deal requires it
    if user_requirements.get("location_required") and not user_context.get("location"):
        validation_result["warnings"].append("User location not available - may affect booking")
    
    status_message = "Booking request validated successfully" if validation_result["valid"] else "Booking request validation failed"
    
    return Command(
        update={
            "conversation_context": {
                "last_validation": validation_result
            },
            "messages": [
                ToolMessage(
                    content=f"{status_message}. Errors: {len(validation_result['errors'])}, Warnings: {len(validation_result['warnings'])}",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def format_deal_response(
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    deals_data: List[Dict[str, Any]],
    format_type: str = "detailed",
    include_booking_info: bool = True,
 
) -> Command:
    """Format deal data for user-friendly presentation.
    
    Args:
        deals_data: Raw deal data to format
        format_type: Format type (summary, detailed, compact)
        include_booking_info: Whether to include booking information
    
    Returns:
        Command with formatted deal response
    """
    
    if not deals_data:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content="No deals data provided to format.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )
    
    formatted_deals = []
    
    for deal in deals_data:
        if format_type == "summary":
            formatted_deal = {
                "title": deal.get("title", "Unknown Deal"),
                "price": deal.get("price"),
                "category": deal.get("category"),
                "rating": deal.get("rating")
            }
        elif format_type == "detailed":
            formatted_deal = {
                **deal,
                "formatted_price": f"€{deal.get('price', 0):.2f}",
                "formatted_rating": f"⭐ {deal.get('rating', 0)}/5" if deal.get('rating') else "No rating"
            }
        else:  # compact
            formatted_deal = {
                "title": deal.get("title", "Unknown Deal"),
                "price": f"€{deal.get('price', 0):.2f}"
            }
        
        if include_booking_info and deal.get("bookable"):
            formatted_deal["booking_available"] = True
            formatted_deal["booking_url"] = deal.get("booking_url")
        
        formatted_deals.append(formatted_deal)
    
    return Command(
        update={
            "conversation_context": {
                "formatted_deals": formatted_deals,
                "format_type": format_type
            },
            "messages": [
                ToolMessage(
                    content=f"Formatted {len(formatted_deals)} deals in {format_type} format.",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )
