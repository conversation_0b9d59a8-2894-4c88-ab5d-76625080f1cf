{"dependencies": ["."], "graphs": {"catchup": "./src/catchup/graph.py:graph", "catchup_v1": "./src/agents/catchup_v1/graph.py:graph", "agent_stream": "./src/agent_stream/implementation.py:agent", "outbond": "./src/outbond/graph.py:graph", "outbond2": "./src/outbond2/graph.py:graph", "research_agent": "./src/research_agent/research_agent.py:agent", "bond_ai": "./src/agents/bond_ai/graph.py:graph"}, "env": ".env"}