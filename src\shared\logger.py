"""
Global logger module for easy logging across the application.

This module provides a pre-configured global logger that can be imported
and used immediately without needing to set up logging in each file.

Usage:
    from shared.logger import logger
    
    logger.info("This is an info message")
    logger.error("This is an error message")
    logger.debug("This is a debug message")

The logger is automatically configured on first import using environment
variables or sensible defaults.
"""

import logging
from .logging_config import get_global_logger

# Create the global logger instance
logger = get_global_logger()

# Export the logger for easy import
__all__ = ['logger']
