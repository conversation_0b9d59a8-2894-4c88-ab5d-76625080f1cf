
from dotenv import load_dotenv
from research_agent.tools import internet_search

from deepagents import create_deep_agent, SubAgent
from research_agent.prompts import *

load_dotenv()

research_sub_agent = {
    "name": "research-agent",
    "description": "Used to research more in depth questions. Only give this researcher one topic at a time. Do not pass multiple sub questions to this researcher. Instead, you should break down a large topic into the necessary components, and then call multiple research agents in parallel, one for each sub question.",
    "prompt": sub_research_prompt,
    "tools": ["internet_search"]
}


critique_sub_agent = {
    "name": "critique-agent",
    "description": "Used to critique the final report. Give this agent some infomration about how you want it to critique the report.",
    "prompt": sub_critique_prompt,
}



# Create the agent
agent = create_deep_agent(
    [internet_search],
    research_instructions_prompt,
    subagents=[critique_sub_agent, research_sub_agent],
).with_config({"recursion_limit": 1000})
