"""Routing Functions for the SDR Agent Graph.

This module contains the routing logic that determines the flow between nodes
in the LangGraph workflow based on the current state and agent decisions.
"""

from typing import Literal

from langchain_core.messages import AIMessage, ToolMessage
from langchain_core.runnables import RunnableConfig

from outbond2 import Configuration
from outbond2 import State




def route_after_agent(
    state: State,
) -> Literal["sdr_quality_validator", "tools", "sdr_research_orchestrator", "__end__"]:
    """Schedule the next node after the agent's action.

    This function determines the next step in the research process based on the
    last message in the state. It handles three main scenarios:

    1. Error recovery: If the last message is unexpectedly not an AIMessage.
    2. Info submission: If the agent has called the "Info" tool to submit findings.
    3. Continued research: If the agent has called any other tool.
    """
    last_message = state.messages[-1]

    # "If for some reason the last message is not an AIMessage (due to a bug or unexpected behavior elsewhere in the code),
    # it ensures the system doesn't crash but instead tries to recover by calling the research orchestrator again.
    if not isinstance(last_message, AIMessage):
        return "sdr_research_orchestrator"
    # If the "Info" tool was called, then the model provided its extraction output. Validate the quality
    if last_message.tool_calls and last_message.tool_calls[0]["name"] == "Info":
        return "sdr_quality_validator"
    # The last message is a tool call that is not "Info" (extraction output)
    else:
        return "tools"


def route_after_checker(
    state: State, config: RunnableConfig
) -> Literal["__end__", "sdr_research_orchestrator"]:
    """Schedule the next node after the checker's evaluation.

    This function determines whether to continue the research process or end it
    based on the checker's evaluation and the current state of the research.
    """
    configurable = Configuration.from_runnable_config(config)
    last_message = state.messages[-1]

    if state.loop_step < configurable.max_loops:
        if not state.info:
            return "sdr_research_orchestrator"
        if not isinstance(last_message, ToolMessage):
            raise ValueError(
                f"{route_after_checker.__name__} expected a tool messages. Received: {type(last_message)}."
            )
        if last_message.status == "error":
            # Research deemed unsatisfactory
            return "sdr_research_orchestrator"
        # It's great!
        return "__end__"
    else:
        return "__end__" 