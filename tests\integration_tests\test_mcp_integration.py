"""Integration tests for MCP tools integration."""

import pytest
from langchain_core.messages import HumanMessage

from catchup import graph
from shared.mcp_tools import get_catchup_tools

pytestmark = pytest.mark.anyio


@pytest.mark.skip(reason="Requires MCP server to be available")
async def test_mcp_tools_loading() -> None:
    """Test that MCP tools can be loaded successfully."""
    tools = await get_catchup_tools()
    assert isinstance(tools, list)
    # Note: tools might be empty if MCP server is not available
    print(f"Loaded {len(tools)} tools from MCP server")


@pytest.mark.skip(reason="Requires MCP server to be available")
async def test_chatbot_with_mcp_tools() -> None:
    """Test chatbot response with MCP tools integration."""
    inputs = {
        "messages": [HumanMessage(content="What categories are available?")],
        "user_id": "test_user_123",
        "email_address": "<EMAIL>",
        "latitude": "45.4642",
        "longitude": "9.1900"
    }
    config = {
        "configurable": {
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant."
        }
    }
    
    res = await graph.ainvoke(inputs, config)
    assert res is not None
    assert "messages" in res
    assert len(res["messages"]) > 0
    
    # Check if the response contains tool calls or tool responses
    messages = res["messages"]
    print(f"Response contains {len(messages)} messages")
    for i, msg in enumerate(messages):
        print(f"Message {i}: {type(msg).__name__}")
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            print(f"  Tool calls: {len(msg.tool_calls)}")
