"""Modern state management for CatchUp v1 agent.

This module defines the enhanced state structure that supports:
- Automatic planning and task breakdown
- Progress tracking and streaming
- Multi-channel communication
- Advanced context management
"""

from __future__ import annotations

from typing import TypedDict, Annotated, List, Optional, Dict, Any, Literal
from pydantic import Field
from typing_extensions import Required
from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from datetime import datetime
import operator


# Core data structures
class Task(TypedDict):
    """Individual task in the agent's plan."""
    id: str
    content: str
    status: Literal["pending", "in_progress", "completed", "failed"]
    priority: Literal["low", "medium", "high", "critical"]
    estimated_duration: Optional[int]  # in seconds
    actual_duration: Optional[int]  # in seconds
    dependencies: Optional[List[str]]  # task IDs this task depends on
    tools_required: Optional[List[str]]  # tools needed for this task
    created_at: str  # ISO timestamp
    started_at: Optional[str]  # ISO timestamp
    completed_at: Optional[str]  # ISO timestamp
    error_message: Optional[str]  # if status is "failed"


class UserContext(TypedDict, total=False):
    """Enhanced user context with preferences and interaction history."""
    user_id: Required[str]
    email_address: Optional[str]
    phone_number: Optional[str]  # for WhatsApp
    location: Optional[Dict[str, float]]  # {"latitude": 45.4666, "longitude": 9.1832}
    preferences: Optional[Dict[str, Any]]  # language, categories, communication preferences
    interaction_history: Optional[List[Dict[str, Any]]]  # previous interactions summary
    current_session_start: Optional[str]  # ISO timestamp
    timezone: Optional[str]  # user's timezone
    communication_preferences: Optional[Dict[str, bool]]  # email, whatsapp, etc.


class SessionConfig(TypedDict, total=False):
    """Session configuration and settings."""
    session_id: Required[str]
    memory_length: Optional[int]  # number of messages to keep in memory
    max_planning_depth: Optional[int]  # maximum task breakdown depth
    communication_enabled: Optional[bool]  # whether to send updates to user
    streaming_enabled: Optional[bool]  # whether to stream progress
    auto_planning: Optional[bool]  # whether to automatically create plans
    tool_timeout: Optional[int]  # tool execution timeout in seconds


class ConversationPhase(TypedDict):
    """Current phase of the conversation."""
    phase: Literal["greeting", "understanding", "planning", "executing", "communicating", "closing"]
    confidence: float  # 0.0 to 1.0
    context: Optional[Dict[str, Any]]  # phase-specific context


class ProgressMetrics(TypedDict):
    """Metrics for tracking agent progress."""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_task_duration: Optional[float]  # in seconds
    total_execution_time: Optional[float]  # in seconds
    tools_used: List[str]  # list of tool names used
    communication_sent: List[str]  # list of communication types sent


class CommunicationLog(TypedDict):
    """Log entry for communications sent to user."""
    id: str
    type: Literal["email", "whatsapp", "stream"]
    recipient: str  # email or phone number
    subject: Optional[str]  # for emails
    content: str
    sent_at: str  # ISO timestamp
    status: Literal["pending", "sent", "failed"]
    error_message: Optional[str]


# State reducers
def tasks_reducer(current: List[Task], new: List[Task]) -> List[Task]:
    """Reducer for tasks list - merge by ID, keeping latest version."""
    if not current:
        return new
    if not new:
        return current
    
    # Create a dict for efficient lookup
    current_dict = {task["id"]: task for task in current}
    
    # Update with new tasks
    for task in new:
        current_dict[task["id"]] = task
    
    return list(current_dict.values())


def communication_log_reducer(current: List[CommunicationLog], new: List[CommunicationLog]) -> List[CommunicationLog]:
    """Reducer for communication log - append new entries."""
    if not current:
        return new
    if not new:
        return current
    return current + new


def context_reducer(current: Optional[Dict[str, Any]], new: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Reducer for context dictionaries - merge with new values taking precedence."""
    if not current:
        return new
    if not new:
        return current
    return {**current, **new}


# Main state class
class CatchUpV1State(TypedDict):
    """Enhanced state for CatchUp v1 agent with modern agentic capabilities."""
    
    # Core conversation
    messages: Annotated[List[AnyMessage], add_messages]
    
    # User and session context
    user_context: Annotated[Optional[Dict[str, Any]], context_reducer] = Field(default_factory=dict)
    session_config: Annotated[Optional[Dict[str, Any]], context_reducer] = Field(default_factory=dict)
    
    # Planning and execution
    current_plan: Annotated[List[Task], tasks_reducer]
    active_task_id: Optional[str] = Field(default=None)  # ID of currently executing task
    
    # Progress tracking
    conversation_phase: Annotated[Optional[Dict[str, Any]], context_reducer] = Field(default_factory=dict)
    progress_metrics: Annotated[Optional[Dict[str, Any]], context_reducer] = Field(default_factory=dict)
    
    # Communication
    communication_log: Annotated[List[CommunicationLog], communication_log_reducer]
    pending_communications: Optional[List[Dict[str, Any]]] = Field(default_factory=list)  # communications to be sent
    
    # Context and memory
    conversation_context: Annotated[Optional[Dict[str, Any]], context_reducer]
    tool_execution_history: Optional[List[Dict[str, Any]]] = Field(default_factory=list)  # history of tool executions
    
    # Intent and routing
    detected_intent: Optional[str] = Field(default=None)
    intent_confidence: Optional[float] = Field(default=None)
    required_capabilities: Optional[List[str]] = Field(default_factory=list)  # capabilities needed for current request
    
    # Error handling and recovery
    error_context: Optional[Dict[str, Any]] = Field(default=None)  # context for error recovery
    retry_count: Annotated[int, operator.add]  # number of retries for current operation


def create_initial_state(
    user_id: str,
    session_id: str,
    email_address: Optional[str] = None,
    phone_number: Optional[str] = None,
    latitude: Optional[str] = None,
    longitude: Optional[str] = None,
    memory_length: Optional[int] = 15
) -> CatchUpV1State:
    """Create initial state for a new conversation."""
    
    # Parse location if provided
    location = None
    if latitude and longitude:
        try:
            location = {
                "latitude": float(latitude),
                "longitude": float(longitude)
            }
        except (ValueError, TypeError):
            location = None
    
    return CatchUpV1State(
        messages=[],
        user_context=UserContext(
            user_id=user_id,
            email_address=email_address,
            phone_number=phone_number,
            location=location,
            current_session_start=datetime.now().isoformat(),
            communication_preferences={"email": True, "whatsapp": True}
        ),
        session_config=SessionConfig(
            session_id=session_id,
            memory_length=memory_length or 15,
            max_planning_depth=5,
            communication_enabled=True,
            streaming_enabled=True,
            auto_planning=True,
            tool_timeout=30
        ),
        current_plan=[],
        active_task_id=None,
        conversation_phase=ConversationPhase(
            phase="greeting",
            confidence=1.0,
            context={}
        ),
        progress_metrics=ProgressMetrics(
            total_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            tools_used=[],
            communication_sent=[]
        ),
        communication_log=[],
        pending_communications=[],
        conversation_context={},
        tool_execution_history=[],
        detected_intent=None,
        intent_confidence=None,
        required_capabilities=None,
        error_context=None,
        retry_count=0
    )
