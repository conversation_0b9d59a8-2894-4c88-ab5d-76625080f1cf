
"""Read table data tool with summarization capabilities."""

from typing import Optional, Any, List, Tuple, Annotated
from catchup.Models.model import Category
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.config import get_stream_writer
import json
from pydantic import BaseModel
from datetime import datetime

from catchup.supabase.client import supabase

@tool
def get_all_categories() -> Tuple[Optional[List[Category]], Optional[str]]:
    """Fetch all categories from the database."""
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call" : "invoking get_all_categories"})
        response = supabase.table('categories').select('id,name,description,icon').execute()

        # Map database columns to Category model
        categories = []
        for item in response.data:
            category = Category(
                categoryId=item['id'],  # Map 'id' to 'categoryId'
                name=item['name'],
                description=item['description'],
                icon=item['icon']
            )
            categories.append(category)

        return categories, None
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)