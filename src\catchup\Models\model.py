from pydantic import BaseModel
from datetime import datetime, date, time


class Category(BaseModel):
    """Category model."""
    categoryId: str
    name: str
    description: str
    icon: str

class UserDetails(BaseModel):
    """User details model."""
    id: str
    email: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone_number: str | None = None
    # Add other fields based on your user_details table schema

class Deal(BaseModel):
    """Deal model for deals_dealid_view."""
    id: str
    title: str | None = None
    description: str | None = None
    category_id: str | None = None
    owner_id: str | None = None
    price: float | None = None
    discount_percentage: float | None = None
    business_name: str | None = None
    location: str | None = None
    availability: str | None = None
    # Add other fields based on your deals_dealid_view schema

class BookingDetails(BaseModel):
    """Booking details model for booking_with_all_details view."""
    booking_id: str | None = None
    deal_title: str | None = None
    deal_description: str | None = None
    business_name: str | None = None
    business_address: str | None = None
    booking_date: date | None = None
    booking_time: time | None = None
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    user_id: str | None = None
    fake: bool | None = None

class ChatMessage(BaseModel):
    """Chat message model for messages table."""
    id: str
    conversation_id: str | None = None
    user_id: str
    content: str
    created_at: datetime
    read_at: datetime | None = None
    metadata: dict = {}