"""SDR Quality Validator Node.

This node validates the quality and SDR-relevance of research output,
ensuring it meets professional standards for sales development use cases.
"""

import json
from typing import Any, Dict, Optional, cast

from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field

from outbond2 import prompts
from outbond2 import State
from outbond2 import get_citation_summary,add_citations_to_response,init_model
from shared import convert_simple_schema_to_json_schema



class InfoIsSatisfactory(BaseModel):
    """Validate whether the current extracted info is satisfactory and complete."""

    reason: list[str] = Field(
        description="First, provide reasoning for why this is either good or bad as a final result. Must include at least 3 reasons."
    )
    is_satisfactory: bool = Field(
        description="After providing your reasoning, provide a value indicating whether the result is satisfactory. If not, you will continue researching."
    )
    improvement_instructions: Optional[str] = Field(
        description="If the result is not satisfactory, provide specific instructions for improvement ONLY within the scope of the defined schema fields."
    )


def _create_schema_aware_validation_prompt(extraction_schema: Dict[str, Any], is_text_format: bool) -> str:
    """Create a schema-aware validation prompt that only evaluates against available fields."""
    
    if is_text_format:
        return """I am thinking of calling the info tool with the text response below. 
Evaluate this response based on:
1. Quality and accuracy of the information provided
2. Relevance for SDR (Sales Development Representative) activities  
3. Completeness within the scope of a text-based response
4. Proper source attribution and citations

Remember: This is a TEXT FORMAT response. Do not request structured data or specific fields that weren't requested.

The response includes information from {citation_summary}.

{presumed_info}"""
    
    # For JSON format, be specific about what fields are available
    json_schema = convert_simple_schema_to_json_schema(extraction_schema)
    available_fields = list(json_schema.get("properties", {}).keys())
    
    # Escape curly braces in JSON schema to prevent format() conflicts
    schema_json_str = json.dumps(json_schema, indent=2).replace("{", "{{").replace("}", "}}")
    
    schema_context = f"""
IMPORTANT: Only evaluate the response against the SPECIFIC FIELDS that were requested in the schema.

REQUESTED SCHEMA FIELDS: {', '.join(available_fields)}

The user has specifically requested only these fields. Do NOT suggest adding fields that are not in this list, such as:
- Executive information (unless specifically requested)
- Recent news (unless specifically requested) 
- Products/services (unless specifically requested)
- Partnerships (unless specifically requested)
- Technology stack (unless specifically requested)
- Or any other fields not explicitly defined in the schema

Focus your evaluation on:
1. Completeness: Are the REQUESTED fields filled with accurate data or appropriately marked as null?
2. Quality: Is the information accurate and well-sourced for the fields that were requested?
3. SDR Relevance: Within the scope of the requested fields, is the information actionable for sales?
4. Citations: Are sources properly attributed?

Schema being used:
{schema_json_str}
"""
    
    return f"""{schema_context}

I am thinking of calling the info tool with the structured data below. 
Is this response satisfactory for the SPECIFIC FIELDS that were requested? 

The response includes information from {{citation_summary}}.

{{presumed_info}}"""


async def sdr_quality_validator(
    state: State, *, config: Optional[RunnableConfig] = None
) -> Dict[str, Any]:
    """Validate the quality and SDR-relevance of the research output.

    This asynchronous function serves as a quality assurance gate that:
    1. Evaluates research completeness for SDR use cases WITHIN THE SCOPE OF THE PROVIDED SCHEMA.
    2. Assesses the actionability of insights for sales professionals.
    3. Validates information quality and citation accuracy.
    4. Determines if the research meets SDR standards or needs improvement.
    5. Ensures final output is valuable for prospect outreach and lead qualification.
    6. Provides specific feedback for research enhancement if needed.
    7. Confirms proper citation integration for source transparency.
    
    IMPORTANT: This validator only evaluates against fields explicitly defined in the extraction schema.
    It will NOT request additional fields that weren't specified by the user.
    """
    # Convert simple schema format to JSON Schema format
    json_schema = convert_simple_schema_to_json_schema(state.extraction_schema)
    
    # Check if this is a text format response
    is_text_format = state.extraction_schema.get("format") == "text"
    
    if is_text_format:
        p = prompts.MAIN_PROMPT.format(
            info="A text response", topic=state.query
        )
    else:
        p = prompts.MAIN_PROMPT.format(
            info=json.dumps(json_schema, indent=2), topic=state.query
        )
    last_message = state.messages[-1]
    if not isinstance(last_message, AIMessage):
        raise ValueError(
            f"{sdr_quality_validator.__name__} expects the last message in the state to be an AI message with tool calls."
            f" Got: {type(last_message)}"
        )
    messages = [HumanMessage(content=p)] + state.messages[:-1]
    presumed_info = state.info
    
    # Include citation information in the checker prompt
    citation_summary = get_citation_summary(state.citations)
    
    # Create schema-aware validation prompt
    checker_prompt = _create_schema_aware_validation_prompt(state.extraction_schema, is_text_format)
    
    p1 = checker_prompt.format(
        presumed_info=json.dumps(presumed_info or {}, indent=2) if not is_text_format else presumed_info,
        citation_summary=citation_summary
    )
    messages.append(HumanMessage(content=p1))
    raw_model = init_model(config)
    bound_model = raw_model.with_structured_output(InfoIsSatisfactory)
    response = cast(InfoIsSatisfactory, await bound_model.ainvoke(messages))
    
    if response.is_satisfactory and presumed_info:
        # Ensure citations are properly integrated in the final output
        final_info = presumed_info
        
        # Double-check that citations are included
        if not is_text_format and isinstance(final_info, dict) and "citations" not in final_info:
            # Add citations if somehow missing
            final_info = add_citations_to_response(final_info, state.citations, is_text_format)
        
        return {
            "info": final_info,
            "messages": [
                ToolMessage(
                    tool_call_id=last_message.tool_calls[0]["id"],
                    content="\n".join(response.reason) + f"\n\nFinal response includes {citation_summary}.",
                    name="Info",
                    additional_kwargs={"artifact": response.model_dump()},
                    status="success",
                )
            ],
        }
    else:
        return {
            "messages": [
                ToolMessage(
                    tool_call_id=last_message.tool_calls[0]["id"],
                    content=f"Unsatisfactory response:\n{response.improvement_instructions}",
                    name="Info",
                    additional_kwargs={"artifact": response.model_dump()},
                    status="error",
                )
            ]
        } 