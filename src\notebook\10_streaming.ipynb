{"cells": [{"cell_type": "code", "execution_count": 2, "id": "48954ab9", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from typing import TypedDict\n"]}, {"cell_type": "code", "execution_count": 3, "id": "61738080", "metadata": {}, "outputs": [], "source": ["class State(TypedDict):\n", "    topics: str\n", "    joke: str\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c9a22627", "metadata": {}, "outputs": [], "source": ["def refin_topic(state:State):\n", "    return {\"topics\":state[\"topics\"] + \" and cats.\"  }"]}, {"cell_type": "code", "execution_count": 5, "id": "505b341e", "metadata": {}, "outputs": [], "source": ["def generate_joke(state:State):\n", "    return {\"joke\": f\"This is a joke about {state['topics']}\"}\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "70c62eef", "metadata": {}, "outputs": [], "source": ["graph = (\n", "    StateGraph(State)\n", "    .add_node(\"refin_topic\", refin_topic)\n", "    .add_node(\"generate_joke\", generate_joke)\n", "    .add_edge(START, \"refin_topic\")\n", "    .add_edge(\"refin_topic\", \"generate_joke\")\n", "    .add_edge(\"generate_joke\", END)\n", ").compile()"]}, {"cell_type": "code", "execution_count": 13, "id": "71217b33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'topics': 'dogs'}\n", "{'topics': 'dogs and cats.'}\n", "{'topics': 'dogs and cats.', 'joke': 'This is a joke about dogs and cats.'}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topics\": \"dogs\"},\n", "    stream_mode=\"values\"\n", "    ):\n", "        print(chunk)"]}, {"cell_type": "code", "execution_count": 17, "id": "de48913f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'refin_topic': {'topics': 'dogs and cats.'}}\n", "{'generate_joke': {'joke': 'This is a joke about dogs and cats.'}}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topics\": \"dogs\"},\n", "    stream_mode=\"updates\"\n", "    ):\n", "        print(chunk)"]}, {"cell_type": "code", "execution_count": 18, "id": "f4a87edb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'step': 1, 'timestamp': '2025-07-30T07:16:18.982962+00:00', 'type': 'task', 'payload': {'id': 'c4730c40-bd9e-3e4d-ec5a-1eed26d7c56b', 'name': 'refin_topic', 'input': {'topics': 'dogs'}, 'triggers': ('branch:to:refin_topic',)}}\n", "{'step': 1, 'timestamp': '2025-07-30T07:16:18.983968+00:00', 'type': 'task_result', 'payload': {'id': 'c4730c40-bd9e-3e4d-ec5a-1eed26d7c56b', 'name': 'refin_topic', 'error': None, 'result': [('topics', 'dogs and cats.')], 'interrupts': []}}\n", "{'step': 2, 'timestamp': '2025-07-30T07:16:18.983968+00:00', 'type': 'task', 'payload': {'id': '707b3b04-14f9-2f5e-8821-ee0ec11680b0', 'name': 'generate_joke', 'input': {'topics': 'dogs and cats.'}, 'triggers': ('branch:to:generate_joke',)}}\n", "{'step': 2, 'timestamp': '2025-07-30T07:16:18.983968+00:00', 'type': 'task_result', 'payload': {'id': '707b3b04-14f9-2f5e-8821-ee0ec11680b0', 'name': 'generate_joke', 'error': None, 'result': [('joke', 'This is a joke about dogs and cats.')], 'interrupts': []}}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topics\": \"dogs\"},\n", "    stream_mode=\"debug\"\n", "    ):\n", "        print(chunk)"]}, {"cell_type": "code", "execution_count": 23, "id": "0db5e580", "metadata": {}, "outputs": [], "source": ["from shared.llm_factory import create_llm\n", "llm = create_llm()  "]}, {"cell_type": "code", "execution_count": 25, "id": "463acfb0", "metadata": {}, "outputs": [], "source": ["def generate_joke(state:State):\n", "    llm_response = llm.invoke(\n", "        f\"Tell me a joke about {state['topics']}\")\n", "    return {\"joke\": llm_response.content}"]}, {"cell_type": "code", "execution_count": 26, "id": "102f71ea", "metadata": {}, "outputs": [], "source": ["graph = (\n", "    StateGraph(State)\n", "    .add_node(\"refin_topic\", refin_topic)\n", "    .add_node(\"generate_joke\", generate_joke)\n", "    .add_edge(START, \"refin_topic\")\n", "    .add_edge(\"refin_topic\", \"generate_joke\")\n", "    .add_edge(\"generate_joke\", END)\n", ").compile()"]}, {"cell_type": "code", "execution_count": 31, "id": "184f15b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sure! Here's a purr-fect one for you:\n", "\n", "Why did the cat sit on the ice cream truck?\n", "\n", "Because it wanted to be a *cool cat*! 🐱🍦😄"]}], "source": ["for message_chunk, metadata in graph.stream(\n", "    {\"topics\": \"ice cream\"},\n", "    stream_mode=\"messages\"\n", "    ):\n", "      if message_chunk.content:\n", "        print(message_chunk.content, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": 33, "id": "bee101bb", "metadata": {}, "outputs": [], "source": ["from langgraph.types import StreamWriter"]}, {"cell_type": "code", "execution_count": 34, "id": "e3500a70", "metadata": {}, "outputs": [], "source": ["def generate_joke(state:State, writer: StreamWriter):\n", "    writer({\"custom key\" :\"custom value}\"})\n", "    llm_response = llm.invoke(\n", "        f\"Tell me a joke about {state['topics']}\")\n", "    return {\"joke\": llm_response.content}"]}, {"cell_type": "code", "execution_count": 36, "id": "4b757042", "metadata": {}, "outputs": [], "source": ["graph = (\n", "    StateGraph(State)\n", "    .add_node(\"refin_topic\", refin_topic)\n", "    .add_node(\"generate_joke\", generate_joke)\n", "    .add_edge(START, \"refin_topic\")\n", "    .add_edge(\"refin_topic\", \"generate_joke\")\n", "    .add_edge(\"generate_joke\", END)\n", ").compile()"]}, {"cell_type": "code", "execution_count": 37, "id": "16ebad40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'custom key': 'custom value}'}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topics\": \"dogs\"},\n", "    stream_mode=\"custom\"\n", "    ):\n", "        print(chunk)"]}, {"cell_type": "code", "execution_count": 38, "id": "931539a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('custom', {'custom key': 'custom value}'})\n", "('messages', (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='Sure', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='!', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' Here', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=\"'s\", additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' a', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' l', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='igh', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='the', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='art', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='ed', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' one', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=':\\n\\n', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='Why', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' don', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='’t', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' dogs', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' make', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' good', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' dancers', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='?\\n\\n', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='Because', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' they', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' have', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' two', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' left', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' p', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='aws', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='!', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='  \\n\\n', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='…', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='But', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' don', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='’t', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' tell', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' the', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' cat', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='.', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='  \\n', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='She', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='’ll', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' just', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' use', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' it', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' as', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' an', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' excuse', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' to', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' show', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' off', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' her', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' *', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='p', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='urr', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='-f', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='ect', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='*', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' moves', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='.', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='🐾', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='🐱', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='💃', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'qwen/qwen3-235b-a22b-2507:free'}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5'), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n", "('messages', (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--2a0e4cf9-c769-4f25-bdd8-b4f00dc7b9d5', usage_metadata={'input_tokens': 17, 'output_tokens': 64, 'total_tokens': 81, 'input_token_details': {}, 'output_token_details': {}}), {'langgraph_step': 2, 'langgraph_node': 'generate_joke', 'langgraph_triggers': ('branch:to:generate_joke',), 'langgraph_path': ('__pregel_pull', 'generate_joke'), 'langgraph_checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'checkpoint_ns': 'generate_joke:e7968991-11fe-cc7e-e5fb-27cc48538528', 'ls_provider': 'openai', 'ls_model_name': 'qwen/qwen3-235b-a22b-07-25:free', 'ls_model_type': 'chat', 'ls_temperature': None}))\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topics\": \"dogs\"},\n", "    stream_mode=[\"messages\", \"custom\"]\n", "    ):\n", "        print(chunk)"]}], "metadata": {"kernelspec": {"display_name": "LanCatchUp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}