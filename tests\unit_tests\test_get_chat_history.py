"""Unit tests for the get_chat_history tool."""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from catchup.tools.get_chat_history import get_chat_history
from catchup.Models.model import ChatMessage


class TestGetChatHistory:
    """Test cases for get_chat_history tool."""

    @patch('catchup.tools.get_chat_history.supabase')
    @patch('catchup.tools.get_chat_history.get_stream_writer')
    def test_get_chat_history_success(self, mock_stream_writer, mock_supabase):
        """Test successful chat history retrieval."""
        # Mock data
        mock_data = [
            {
                'id': 'msg1',
                'conversation_id': 'conv1',
                'user_id': 'user1',
                'content': 'Hello',
                'created_at': '2024-01-01T10:00:00Z',
                'read_at': None,
                'metadata': {}
            },
            {
                'id': 'msg2',
                'conversation_id': 'conv1',
                'user_id': 'user2',
                'content': 'Hi there',
                'created_at': '2024-01-01T10:01:00Z',
                'read_at': '2024-01-01T10:02:00Z',
                'metadata': {'type': 'reply'}
            }
        ]
        
        # Mock Supabase response
        mock_response = Mock()
        mock_response.data = mock_data
        mock_supabase.table.return_value.select.return_value.eq.return_value.order.return_value.execute.return_value = mock_response
        
        # Mock stream writer
        mock_stream_writer.return_value = Mock()
        
        # Call the function
        result, error = get_chat_history.func('conv1')
        
        # Assertions
        assert error is None
        assert len(result) == 2
        assert isinstance(result[0], ChatMessage)
        assert result[0].id == 'msg1'
        assert result[0].content == 'Hello'
        assert result[1].id == 'msg2'
        assert result[1].content == 'Hi there'

    @patch('catchup.tools.get_chat_history.supabase')
    @patch('catchup.tools.get_chat_history.get_stream_writer')
    def test_get_chat_history_with_limit(self, mock_stream_writer, mock_supabase):
        """Test chat history retrieval with limit."""
        # Mock Supabase response
        mock_response = Mock()
        mock_response.data = []
        
        # Create a chain of mocks for the query builder
        mock_query = Mock()
        mock_query.limit.return_value.execute.return_value = mock_response
        mock_supabase.table.return_value.select.return_value.eq.return_value.order.return_value = mock_query
        
        # Mock stream writer
        mock_stream_writer.return_value = Mock()
        
        # Call the function with limit
        result, error = get_chat_history.func('conv1', limit=5)
        
        # Verify limit was called
        mock_query.limit.assert_called_once_with(5)
        assert error is None
        assert result == []

    @patch('catchup.tools.get_chat_history.supabase')
    @patch('catchup.tools.get_chat_history.get_stream_writer')
    def test_get_chat_history_empty_result(self, mock_stream_writer, mock_supabase):
        """Test chat history retrieval with no messages."""
        # Mock empty Supabase response
        mock_response = Mock()
        mock_response.data = []
        mock_supabase.table.return_value.select.return_value.eq.return_value.order.return_value.execute.return_value = mock_response
        
        # Mock stream writer
        mock_stream_writer.return_value = Mock()
        
        # Call the function
        result, error = get_chat_history.func('nonexistent_conv')
        
        # Assertions
        assert error is None
        assert result == []

    @patch('catchup.tools.get_chat_history.supabase')
    @patch('catchup.tools.get_chat_history.get_stream_writer')
    def test_get_chat_history_database_error(self, mock_stream_writer, mock_supabase):
        """Test chat history retrieval with database error."""
        # Mock database error
        mock_supabase.table.return_value.select.return_value.eq.return_value.order.return_value.execute.side_effect = Exception("Database error")
        
        # Mock stream writer
        mock_stream_writer.return_value = Mock()
        
        # Call the function
        result, error = get_chat_history.func('conv1')
        
        # Assertions
        assert result is None
        assert error == "Database error"

    @patch('catchup.tools.get_chat_history.supabase')
    def test_get_chat_history_no_stream_writer(self, mock_supabase):
        """Test chat history retrieval when stream writer is not available."""
        # Mock Supabase response
        mock_response = Mock()
        mock_response.data = []
        mock_supabase.table.return_value.select.return_value.eq.return_value.order.return_value.execute.return_value = mock_response
        
        # Call the function (stream writer will fail but should be handled gracefully)
        result, error = get_chat_history.func('conv1')
        
        # Assertions - should still work without stream writer
        assert error is None
        assert result == []
