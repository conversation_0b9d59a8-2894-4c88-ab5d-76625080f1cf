"""Configuration management for CatchUp v1 agent."""

from __future__ import annotations

from dataclasses import dataclass, field, fields
from typing import Annotated, Optional, List, Dict, Any
from langchain_core.runnables import RunnableConfig, ensure_config


@dataclass(kw_only=True)
class CatchUpV1Configuration:
    """Configuration for the CatchUp v1 agent."""

    # Model configuration
    model_name: Annotated[str, {"__template_metadata__": {"kind": "llm"}}] = field(
        default="openai/gpt-4o-mini",
        metadata={
            "description": "The name of the language model to use for the agent. "
            "Should be in the form: provider/model-name."
        },
    )
    
    model_temperature: float = field(
        default=0.1,
        metadata={
            "description": "Temperature for the language model (0.0 to 1.0). "
            "Lower values make the model more deterministic."
        }
    )
    
    # Planning configuration
    max_planning_depth: int = field(
        default=5,
        metadata={
            "description": "Maximum depth for task breakdown in planning phase."
        }
    )
    
    auto_planning_enabled: bool = field(
        default=True,
        metadata={
            "description": "Whether to automatically create plans for user requests."
        }
    )
    
    # Communication configuration
    communication_enabled: bool = field(
        default=True,
        metadata={
            "description": "Whether to send progress updates to users via email/WhatsApp."
        }
    )
    
    streaming_enabled: bool = field(
        default=True,
        metadata={
            "description": "Whether to stream progress updates in real-time."
        }
    )
    
    # Tool configuration
    tool_timeout: int = field(
        default=30,
        metadata={
            "description": "Timeout for tool execution in seconds."
        }
    )
    
    max_tool_retries: int = field(
        default=3,
        metadata={
            "description": "Maximum number of retries for failed tool executions."
        }
    )
    
    # Memory configuration
    default_memory_length: int = field(
        default=15,
        metadata={
            "description": "Default number of messages to keep in conversation memory."
        }
    )
    
    # System prompts
    system_prompt: str = field(
        default="""You are CatchUp v1, an advanced AI customer service agent for a marketplace platform.

Your capabilities include:
- Automatic planning and task breakdown for complex requests
- Real-time progress streaming to keep users informed
- Multi-channel communication (email, WhatsApp) with plan updates
- Advanced tool orchestration for marketplace operations
- Contextual memory and session management

Core Principles:
1. Always create a clear plan for multi-step requests
2. Keep users informed of progress through streaming updates
3. Use appropriate tools efficiently and handle errors gracefully
4. Maintain context across the conversation
5. Communicate proactively when sending emails or WhatsApp messages

Available marketplace tools:
- Category and deal management
- User profile and booking operations
- Business information retrieval
- Communication tools (email, WhatsApp)
- Search and filtering capabilities

Always respond in the user's preferred language (default: Italian for Italian marketplace).
Be helpful, professional, and proactive in solving customer needs.""",
        metadata={
            "description": "System prompt for the CatchUp v1 agent."
        }
    )
    
    planner_prompt: str = field(
        default="""You are the Planning component of CatchUp v1. Your role is to break down user requests into actionable tasks.

For each user request, create a detailed plan with:
1. Clear, specific tasks that can be executed independently
2. Appropriate priority levels (low, medium, high, critical)
3. Estimated duration for each task
4. Required tools for each task
5. Dependencies between tasks

Guidelines:
- Keep tasks atomic and focused
- Prioritize user-facing tasks higher
- Consider tool availability and limitations
- Plan for error handling and communication
- Include progress communication tasks

Output format: List of Task objects with all required fields.""",
        metadata={
            "description": "System prompt for the planning component."
        }
    )
    
    # Advanced configuration
    enable_proactive_communication: bool = field(
        default=True,
        metadata={
            "description": "Whether to proactively send updates to users during long operations."
        }
    )
    
    communication_interval: int = field(
        default=30,
        metadata={
            "description": "Interval in seconds for proactive communication during long operations."
        }
    )
    
    error_recovery_enabled: bool = field(
        default=True,
        metadata={
            "description": "Whether to attempt automatic error recovery."
        }
    )
    
    debug_mode: bool = field(
        default=False,
        metadata={
            "description": "Whether to enable debug mode with additional logging."
        }
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "CatchUpV1Configuration":
        """Load configuration with defaults for the given invocation."""
        config = ensure_config(config)
        configurable = config.get("configurable") or {}
        _fields = {f.name for f in fields(cls) if f.init}
        return cls(**{k: v for k, v in configurable.items() if k in _fields})
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in fields(self)
            if field.init
        }
    
    def update(self, **kwargs) -> "CatchUpV1Configuration":
        """Create a new configuration with updated values."""
        current_values = self.to_dict()
        current_values.update(kwargs)
        return self.__class__(**current_values)
