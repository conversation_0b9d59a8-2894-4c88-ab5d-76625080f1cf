# CatchUp v1 - Modern AI Agentic Design

A sophisticated AI agent system built with LangGraph that provides advanced customer service capabilities for marketplace platforms with modern agentic design patterns.

## 🎯 Key Features

### Modern Agentic Capabilities
- **Automatic Planning**: Breaks down complex requests into actionable tasks
- **Real-time Progress Streaming**: Keeps users informed with live updates
- **Multi-channel Communication**: Proactive email and WhatsApp notifications
- **Advanced Tool Orchestration**: Smart tool selection and execution
- **Enhanced Memory & Context**: Sophisticated session and user context management
- **Error Recovery**: Graceful handling of failures with retry logic

### Marketplace Integration
- **MCP Tools Integration**: Seamless integration with existing CatchUp MCP tools
- **Enhanced Search**: Location-aware deal search with advanced filtering
- **Booking Management**: Intelligent booking validation and processing
- **User Context Awareness**: Personalized responses based on user preferences
- **Business Intelligence**: Advanced business and category management

## 🏗️ Architecture

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Planner      │    │    Executor     │    │  Communicator   │
│                 │    │                 │    │                 │
│ • Task breakdown│    │ • Task execution│    │ • Email/WhatsApp│
│ • Dependency    │    │ • Progress track│    │ • Status updates│
│   management    │    │ • Error handling│    │ • Plan summaries│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Call Model    │
                    │                 │
                    │ • LLM with tools│
                    │ • Context-aware │
                    │ • Smart routing │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Tools Node    │
                    │                 │
                    │ • Tool execution│
                    │ • Progress track│
                    │ • Error recovery│
                    └─────────────────┘
```

### State Management

The agent uses an enhanced state structure that tracks:

- **Planning State**: Current plan, active tasks, progress metrics
- **User Context**: Profile, preferences, location, communication settings
- **Session Config**: Memory length, timeouts, feature flags
- **Communication Log**: Email/WhatsApp delivery tracking
- **Execution History**: Tool usage, performance metrics, error context

### Flow Control

1. **Entry**: Determines if planning is needed based on request complexity
2. **Planning**: Creates detailed task breakdown with dependencies
3. **Execution**: Manages task execution with progress tracking
4. **Communication**: Sends proactive updates to users
5. **Tool Integration**: Executes marketplace operations with error handling

## 🚀 Getting Started

### Installation

The agent is integrated into the existing CatchUp system. No additional installation required.

### Configuration

Configure the agent in `langgraph.json`:

```json
{
  "graphs": {
    "catchup_v1": "./agents/catchup_v1/graph.py:graph"
  }
}
```

### Usage

#### Basic Usage

```python
from agents.catchup_v1.graph import graph
from agents.catchup_v1.state import create_initial_state

# Create initial state
state = create_initial_state(
    user_id="user123",
    session_id="session456",
    email_address="<EMAIL>",
    latitude="45.4666",
    longitude="9.1832"
)

# Add user message
state["messages"] = [HumanMessage(content="Find me a restaurant and book a table")]

# Configure agent
config = {
    "configurable": {
        "model_name": "openai/gpt-4o-mini",
        "auto_planning_enabled": True,
        "communication_enabled": True
    }
}

# Run agent
result = await graph.ainvoke(state, config)
```

#### Advanced Configuration

```python
config = {
    "configurable": {
        "model_name": "openai/gpt-4o-mini",
        "model_temperature": 0.1,
        "max_planning_depth": 5,
        "auto_planning_enabled": True,
        "communication_enabled": True,
        "streaming_enabled": True,
        "tool_timeout": 30,
        "max_tool_retries": 3,
        "enable_proactive_communication": True,
        "communication_interval": 30,
        "error_recovery_enabled": True,
        "debug_mode": False
    }
}
```

## 🛠️ Tools & Capabilities

### Planning Tools
- `create_plan`: Break down requests into actionable tasks
- `update_task_status`: Track task progress and completion
- `get_current_plan`: Retrieve current plan status

### Communication Tools
- `send_progress_update`: Send real-time progress updates
- `send_plan_summary`: Send comprehensive plan summaries
- `stream_status_update`: Stream structured status updates

### Enhanced Marketplace Tools
- `enhanced_search_deals`: Advanced deal search with location filtering
- `get_user_context_info`: Comprehensive user context retrieval
- `validate_booking_request`: Intelligent booking validation
- `format_deal_response`: User-friendly deal formatting

### Inherited MCP Tools
- All existing CatchUp MCP tools (categories, deals, bookings, communication)
- Enhanced with better error handling and progress tracking

## 📊 Monitoring & Analytics

### Progress Tracking
- Task completion rates
- Average execution times
- Tool usage statistics
- Communication delivery status

### Error Handling
- Automatic retry logic
- Graceful degradation
- Error context preservation
- Recovery strategies

### Performance Metrics
- Response times
- Success rates
- User satisfaction indicators
- Resource utilization

## 🔧 Development

### Testing

Run the test suite:

```bash
python agents/catchup_v1/test_agent.py
```

### Extending the Agent

#### Adding New Tools

1. Create tool in `agents/catchup_v1/tools/`
2. Add to appropriate tool loader
3. Update tool binding in `call_model_node`

#### Adding New Nodes

1. Create node in `agents/catchup_v1/nodes/`
2. Add to graph in `graph.py`
3. Update routing logic

#### Customizing Behavior

Modify configuration in `configuration.py` or override at runtime.

## 🤝 Integration

### Backward Compatibility
- Maintains compatibility with existing CatchUp system
- Can run alongside original CatchUp agent
- Shares MCP tools and infrastructure

### Migration Path
- Gradual migration from original CatchUp
- A/B testing capabilities
- Feature flag support

## 📈 Performance

### Optimizations
- Efficient state management with reducers
- Smart tool binding after LLM creation
- Intelligent routing to minimize unnecessary calls
- Proactive communication to reduce user wait times

### Scalability
- Stateless design for horizontal scaling
- Configurable timeouts and limits
- Resource-aware execution

## 🔒 Security & Privacy

### Data Protection
- User context encryption
- Secure communication channels
- Privacy-aware logging

### Access Control
- Tool-level permissions
- User-based restrictions
- Audit trail maintenance

## 📚 Documentation

- **Architecture**: Detailed system design and flow
- **API Reference**: Complete tool and node documentation
- **Configuration**: All available settings and options
- **Examples**: Real-world usage scenarios
- **Troubleshooting**: Common issues and solutions

## 🎉 Benefits Over Original CatchUp

1. **Proactive Planning**: Automatic task breakdown for complex requests
2. **Real-time Updates**: Live progress streaming to users
3. **Enhanced Communication**: Multi-channel notifications with rich formatting
4. **Better Error Handling**: Graceful recovery with retry logic
5. **Advanced Context**: Sophisticated user and session management
6. **Performance Monitoring**: Comprehensive metrics and analytics
7. **Extensibility**: Modern, modular architecture for easy enhancement

---

*CatchUp v1 represents the next generation of AI customer service agents, combining the reliability of the original system with modern agentic design patterns for superior user experience.*
