"""Planning and task management tools for CatchUp v1."""

from __future__ import annotations

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any, Annotated
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from catchup_v1.state import CatchUpV1State, Task


@tool
def create_plan(
    tasks: List[Dict[str, Any]],
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Create a comprehensive plan by breaking down the user request into actionable tasks.
    
    Args:
        tasks: List of task dictionaries with keys:
            - content: str - Description of the task
            - priority: str - Priority level (low, medium, high, critical)
            - estimated_duration: int - Estimated duration in seconds
            - tools_required: List[str] - Tools needed for this task
            - dependencies: List[str] - Task IDs this task depends on (optional)
    
    Returns:
        Command to update the state with the new plan
    """
    
    # Convert task dictionaries to Task objects
    plan_tasks = []
    current_time = datetime.now().isoformat()
    
    for i, task_data in enumerate(tasks):
        task = Task(
            id=str(uuid.uuid4()),
            content=task_data["content"],
            status="pending",
            priority=task_data.get("priority", "medium"),
            estimated_duration=task_data.get("estimated_duration"),
            actual_duration=None,
            dependencies=task_data.get("dependencies", []),
            tools_required=task_data.get("tools_required", []),
            created_at=current_time,
            started_at=None,
            completed_at=None,
            error_message=None
        )
        plan_tasks.append(task)
    
    # Update progress metrics
    current_metrics = state.get("progress_metrics", {})
    updated_metrics = {
        **current_metrics,
        "total_tasks": len(plan_tasks),
        "completed_tasks": 0,
        "failed_tasks": 0
    }
    
    # Update conversation phase
    updated_phase = {
        "phase": "planning",
        "confidence": 0.9,
        "context": {"plan_created_at": current_time, "total_tasks": len(plan_tasks)}
    }
    
    return Command(
        update={
            "current_plan": plan_tasks,
            "progress_metrics": updated_metrics,
            "conversation_phase": updated_phase,
            "messages": [
                ToolMessage(
                    content=f"Created comprehensive plan with {len(plan_tasks)} tasks. Ready to begin execution.",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def update_task_status(
    task_id: str,
    status: str,
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    error_message: Optional[str] = None,
    actual_duration: Optional[int] = None,
) -> Command:
    """Update the status of a specific task in the current plan.
    
    Args:
        task_id: ID of the task to update
        status: New status (pending, in_progress, completed, failed)
        error_message: Error message if status is failed
        actual_duration: Actual duration in seconds if task is completed
    
    Returns:
        Command to update the task status in state
    """
    
    current_plan = state.get("current_plan", [])
    current_time = datetime.now().isoformat()
    
    # Find and update the task
    updated_plan = []
    task_found = False
    
    for task in current_plan:
        if task["id"] == task_id:
            task_found = True
            updated_task = {**task}
            updated_task["status"] = status
            
            if status == "in_progress" and not task.get("started_at"):
                updated_task["started_at"] = current_time
            elif status in ["completed", "failed"]:
                updated_task["completed_at"] = current_time
                if actual_duration:
                    updated_task["actual_duration"] = actual_duration
                if error_message:
                    updated_task["error_message"] = error_message
            
            updated_plan.append(updated_task)
        else:
            updated_plan.append(task)
    
    if not task_found:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content=f"Error: Task with ID {task_id} not found in current plan.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )
    
    # Update progress metrics
    current_metrics = state.get("progress_metrics", {})
    completed_count = sum(1 for task in updated_plan if task["status"] == "completed")
    failed_count = sum(1 for task in updated_plan if task["status"] == "failed")
    
    updated_metrics = {
        **current_metrics,
        "completed_tasks": completed_count,
        "failed_tasks": failed_count
    }
    
    # Calculate average task duration
    completed_tasks_with_duration = [
        task for task in updated_plan 
        if task["status"] == "completed" and task.get("actual_duration")
    ]
    if completed_tasks_with_duration:
        avg_duration = sum(task["actual_duration"] for task in completed_tasks_with_duration) / len(completed_tasks_with_duration)
        updated_metrics["average_task_duration"] = avg_duration
    
    # Update active task ID
    active_task_id = None
    if status == "in_progress":
        active_task_id = task_id
    elif status in ["completed", "failed"] and state.get("active_task_id") == task_id:
        # Find next pending task
        for task in updated_plan:
            if task["status"] == "pending":
                # Check if dependencies are met
                dependencies_met = True
                for dep_id in task.get("dependencies", []):
                    dep_task = next((t for t in updated_plan if t["id"] == dep_id), None)
                    if not dep_task or dep_task["status"] != "completed":
                        dependencies_met = False
                        break
                
                if dependencies_met:
                    active_task_id = task["id"]
                    break
    
    return Command(
        update={
            "current_plan": updated_plan,
            "progress_metrics": updated_metrics,
            "active_task_id": active_task_id,
            "messages": [
                ToolMessage(
                    content=f"Updated task {task_id} status to {status}. Progress: {completed_count}/{len(updated_plan)} tasks completed.",
                    tool_call_id=tool_call_id
                )
            ]
        }
    )


@tool
def get_current_plan(
    state: Annotated[CatchUpV1State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Get the current plan with task statuses and progress information.
    
    Returns:
        Command with current plan information
    """
    
    current_plan = state.get("current_plan", [])
    progress_metrics = state.get("progress_metrics", {})
    active_task_id = state.get("active_task_id")
    
    if not current_plan:
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content="No current plan exists. Create a plan first using the create_plan tool.",
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )
    
    # Format plan summary
    plan_summary = []
    plan_summary.append(f"Current Plan Summary ({len(current_plan)} tasks):")
    plan_summary.append(f"Progress: {progress_metrics.get('completed_tasks', 0)}/{progress_metrics.get('total_tasks', 0)} completed")
    
    if progress_metrics.get("failed_tasks", 0) > 0:
        plan_summary.append(f"Failed: {progress_metrics['failed_tasks']} tasks")
    
    plan_summary.append("\nTasks:")
    
    for i, task in enumerate(current_plan, 1):
        status_emoji = {
            "pending": "⏳",
            "in_progress": "🔄", 
            "completed": "✅",
            "failed": "❌"
        }.get(task["status"], "❓")
        
        active_indicator = " (ACTIVE)" if task["id"] == active_task_id else ""
        priority_indicator = f" [{task['priority'].upper()}]" if task.get("priority") != "medium" else ""
        
        plan_summary.append(f"{i}. {status_emoji} {task['content']}{priority_indicator}{active_indicator}")
        
        if task.get("error_message"):
            plan_summary.append(f"   Error: {task['error_message']}")
    
    return Command(
        update={
            "messages": [
                ToolMessage(
                    content="\n".join(plan_summary),
                    tool_call_id=tool_call_id
                )
            ]
        }
    )
