"""Tools Node Configuration.

This module provides the tools node configuration for the LangGraph workflow.
It centralizes all tool management and makes it easy to add or modify tools.
"""

from langgraph.prebuilt import ToolNode

from outbond2.tools import search,scrape_website


def create_tools_node() -> ToolNode:
    """Create and configure the tools node with all available tools.
    
    Returns:
        ToolNode: Configured node with all available tools for the SDR agent.
    """
    return ToolNode([search, scrape_website]) 