"""Unit tests for memory functionality in the call_model node and state reducer."""

import pytest
from langchain_core.messages import HumanMessage, AIMessage
from catchup.nodes.call_model import limit_message_history
from catchup.state import memory_aware_reducer


class TestMemoryFunctionality:
    """Test cases for memory limiting functionality."""

    def test_limit_message_history_basic(self):
        """Test basic message history limiting."""
        messages = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
            HumanMessage(content="Message 2"),
            AIMessage(content="Response 2"),
            HumanMessage(content="Message 3"),
            AIMessage(content="Response 3"),
        ]
        
        # Test limiting to 3 messages
        limited = limit_message_history(messages, 3)
        assert len(limited) == 3
        assert limited[0].content == "Response 2"
        assert limited[-1].content == "Response 3"

    def test_limit_message_history_larger_than_available(self):
        """Test when memory limit is larger than available messages."""
        messages = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]
        
        limited = limit_message_history(messages, 10)
        assert len(limited) == 2
        assert limited[0].content == "Message 1"
        assert limited[1].content == "Response 1"

    def test_limit_message_history_zero_limit(self):
        """Test with zero memory limit."""
        messages = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]
        
        limited = limit_message_history(messages, 0)
        assert len(limited) == 0

    def test_limit_message_history_negative_limit(self):
        """Test with negative memory limit."""
        messages = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]
        
        limited = limit_message_history(messages, -5)
        assert len(limited) == 0

    def test_limit_message_history_empty_list(self):
        """Test with empty message list."""
        messages = []
        
        limited = limit_message_history(messages, 5)
        assert len(limited) == 0

    def test_limit_message_history_single_message(self):
        """Test with single message."""
        messages = [HumanMessage(content="Single message")]
        
        limited = limit_message_history(messages, 5)
        assert len(limited) == 1
        assert limited[0].content == "Single message"

    def test_limit_message_history_preserves_order(self):
        """Test that message order is preserved."""
        messages = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
            HumanMessage(content="Message 2"),
            AIMessage(content="Response 2"),
            HumanMessage(content="Message 3"),
            AIMessage(content="Response 3"),
        ]
        
        limited = limit_message_history(messages, 4)
        assert len(limited) == 4
        
        # Check that order is preserved
        expected_contents = ["Message 2", "Response 2", "Message 3", "Response 3"]
        actual_contents = [msg.content for msg in limited]
        assert actual_contents == expected_contents

    def test_limit_message_history_keeps_most_recent(self):
        """Test that the most recent messages are kept."""
        messages = []
        for i in range(10):
            messages.append(HumanMessage(content=f"Message {i+1}"))
            messages.append(AIMessage(content=f"Response {i+1}"))
        
        # Total: 20 messages, limit to 6
        limited = limit_message_history(messages, 6)
        assert len(limited) == 6
        
        # Should keep the last 6 messages (from Message 8 onwards)
        expected_first = "Message 8"
        expected_last = "Response 10"
        
        assert limited[0].content == expected_first
        assert limited[-1].content == expected_last

    def test_memory_length_parsing_scenarios(self):
        """Test various memory length parsing scenarios."""
        # This tests the logic that would be used in call_model
        test_cases = [
            ("15", 15),
            ("5", 5),
            ("0", 0),
            ("-1", -1),
            ("invalid", 15),  # Should default to 15
            ("", 15),         # Should default to 15
            ("3.5", 15),      # Should default to 15
        ]
        
        for memory_lenght_str, expected in test_cases:
            try:
                memory_length = int(memory_lenght_str)
            except (ValueError, TypeError):
                memory_length = 15
            
            assert memory_length == expected, f"Failed for input '{memory_lenght_str}'"


class TestMemoryAwareReducer:
    """Test cases for the memory-aware reducer functionality."""

    def test_memory_aware_reducer_basic(self):
        """Test basic reducer functionality."""
        existing = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]
        new = [
            HumanMessage(content="Message 2"),
            AIMessage(content="Response 2"),
        ]

        result = memory_aware_reducer(existing, new)
        assert len(result) == 4
        assert result[0].content == "Message 1"
        assert result[-1].content == "Response 2"

    def test_memory_aware_reducer_empty_existing(self):
        """Test reducer with empty existing messages."""
        existing = []
        new = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]

        result = memory_aware_reducer(existing, new)
        assert len(result) == 2
        assert result[0].content == "Message 1"
        assert result[1].content == "Response 1"

    def test_memory_aware_reducer_empty_new(self):
        """Test reducer with empty new messages."""
        existing = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]
        new = []

        result = memory_aware_reducer(existing, new)
        assert len(result) == 2
        assert result[0].content == "Message 1"
        assert result[1].content == "Response 1"

    def test_memory_aware_reducer_exceeds_limit(self):
        """Test reducer when combined messages exceed the default limit of 15."""
        # Create 10 existing messages
        existing = []
        for i in range(10):
            existing.append(HumanMessage(content=f"Existing {i+1}"))

        # Add 10 new messages (total would be 20, exceeds limit of 15)
        new = []
        for i in range(10):
            new.append(HumanMessage(content=f"New {i+1}"))

        result = memory_aware_reducer(existing, new)

        # Should only keep the most recent 15 messages
        assert len(result) == 15

        # Should start from "Existing 6" (keeping last 15 out of 20)
        assert result[0].content == "Existing 6"
        assert result[-1].content == "New 10"

    def test_memory_aware_reducer_within_limit(self):
        """Test reducer when combined messages are within the limit."""
        existing = [
            HumanMessage(content="Message 1"),
            AIMessage(content="Response 1"),
        ]
        new = [
            HumanMessage(content="Message 2"),
            AIMessage(content="Response 2"),
        ]

        result = memory_aware_reducer(existing, new)

        # Should keep all messages since total (4) is less than limit (15)
        assert len(result) == 4
        assert result[0].content == "Message 1"
        assert result[-1].content == "Response 2"

    def test_memory_aware_reducer_preserves_order(self):
        """Test that the reducer preserves message order."""
        existing = [
            HumanMessage(content="A"),
            AIMessage(content="B"),
            HumanMessage(content="C"),
        ]
        new = [
            AIMessage(content="D"),
            HumanMessage(content="E"),
        ]

        result = memory_aware_reducer(existing, new)

        expected_order = ["A", "B", "C", "D", "E"]
        actual_order = [msg.content for msg in result]

        assert actual_order == expected_order
