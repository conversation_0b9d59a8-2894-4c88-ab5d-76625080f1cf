"""Communicator node for CatchUp v1 agent."""

from __future__ import annotations

from typing import Dict, Any, List
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage

from agents.catchup_v1.state import CatchUpV1State, CommunicationLog
from agents.catchup_v1.configuration import CatchUpV1Configuration
from shared.mcp_tools import get_catchup_tools_by_names


async def communicator_node(state: CatchUpV1State, config: RunnableConfig) -> Dict[str, Any]:
    """Communicator node that handles sending updates to users via email/WhatsApp.
    
    This node:
    1. Processes pending communications from the queue
    2. Sends emails and WhatsApp messages using MCP tools
    3. Updates communication logs with delivery status
    4. Handles communication failures gracefully
    5. Manages communication preferences and throttling
    """
    
    configuration = CatchUpV1Configuration.from_runnable_config(config)
    pending_communications = state.get("pending_communications", [])
    user_context = state.get("user_context", {})
    session_config = state.get("session_config", {})
    communication_log = state.get("communication_log", [])
    
    if not pending_communications:
        return {
            "messages": [AIMessage(content="No pending communications to process.")],
            "conversation_phase": {
                "phase": "executing",
                "confidence": 0.8,
                "context": {"no_pending_communications": True}
            }
        }
    
    # Check if communication is enabled
    if not session_config.get("communication_enabled", True):
        return {
            "pending_communications": [],  # Clear the queue
            "messages": [AIMessage(content="Communication is disabled for this session. Pending communications cleared.")],
        }
    
    # Get communication tools
    try:
        communication_tools = await get_catchup_tools_by_names(["sent_email_to_users", "whatsapps_sent_tool"])
        email_tool = next((tool for tool in communication_tools if tool.name == "sent_email_to_users"), None)
        whatsapp_tool = next((tool for tool in communication_tools if tool.name == "whatsapps_sent_tool"), None)
    except Exception as e:
        return {
            "messages": [AIMessage(content=f"Failed to load communication tools: {str(e)}")],
            "error_context": {
                "node": "communicator",
                "error": "tool_loading_failed",
                "details": str(e)
            }
        }
    
    processed_communications = []
    updated_log = list(communication_log)  # Copy existing log
    successful_sends = 0
    failed_sends = 0
    
    # Process each pending communication
    for comm in pending_communications:
        comm_type = comm.get("type")
        recipient = comm.get("recipient")
        content = comm.get("content")
        subject = comm.get("subject")
        urgent = comm.get("urgent", False)
        
        # Create log entry
        log_entry = CommunicationLog(
            id=f"comm_{int(datetime.now().timestamp())}_{len(updated_log)}",
            type=comm_type,
            recipient=recipient,
            subject=subject,
            content=content,
            sent_at=datetime.now().isoformat(),
            status="pending",
            error_message=None
        )
        
        try:
            if comm_type == "email" and email_tool:
                success = await _send_email(
                    email_tool,
                    recipient,
                    subject or "CatchUp Update",
                    content,
                    user_context
                )
                
            elif comm_type == "whatsapp" and whatsapp_tool:
                success = await _send_whatsapp(
                    whatsapp_tool,
                    recipient,
                    content,
                    user_context
                )
                
            else:
                success = False
                log_entry["error_message"] = f"No tool available for {comm_type} communication"
            
            if success:
                log_entry["status"] = "sent"
                successful_sends += 1
            else:
                log_entry["status"] = "failed"
                failed_sends += 1
                
        except Exception as e:
            log_entry["status"] = "failed"
            log_entry["error_message"] = str(e)
            failed_sends += 1
        
        updated_log.append(log_entry)
        processed_communications.append(comm)
    
    # Update progress metrics
    current_metrics = state.get("progress_metrics", {})
    communication_sent = current_metrics.get("communication_sent", [])
    
    for comm in processed_communications:
        if comm["type"] not in communication_sent:
            communication_sent.append(comm["type"])
    
    updated_metrics = {
        **current_metrics,
        "communication_sent": communication_sent
    }
    
    # Create summary message
    if successful_sends > 0 or failed_sends > 0:
        summary_parts = []
        summary_parts.append(f"📤 Communication processing completed:")
        
        if successful_sends > 0:
            summary_parts.append(f"✅ Successfully sent: {successful_sends} messages")
        
        if failed_sends > 0:
            summary_parts.append(f"❌ Failed to send: {failed_sends} messages")
        
        summary_message = "\n".join(summary_parts)
    else:
        summary_message = "📤 No communications were processed."
    
    # Update conversation phase
    conversation_phase = {
        "phase": "communicating",
        "confidence": 0.9 if failed_sends == 0 else 0.7,
        "context": {
            "communications_processed": len(processed_communications),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "processed_at": datetime.now().isoformat()
        }
    }
    
    return {
        "pending_communications": [],  # Clear the queue
        "communication_log": updated_log,
        "progress_metrics": updated_metrics,
        "conversation_phase": conversation_phase,
        "messages": [AIMessage(content=summary_message)]
    }


async def _send_email(
    email_tool,
    recipient: str,
    subject: str,
    content: str,
    user_context: Dict[str, Any]
) -> bool:
    """Send an email using the MCP email tool."""
    
    try:
        # Prepare email parameters
        email_params = {
            "to": recipient,
            "subject": subject,
            "html_content": _format_email_content(content, user_context),
            "from_name": "CatchUp Customer Service"
        }
        
        # Execute the email tool
        result = await email_tool.ainvoke(email_params)
        
        # Check if the result indicates success
        # This depends on the specific implementation of the MCP tool
        return True  # Assume success for now
        
    except Exception as e:
        print(f"Email sending failed: {str(e)}")
        return False


async def _send_whatsapp(
    whatsapp_tool,
    recipient: str,
    content: str,
    user_context: Dict[str, Any]
) -> bool:
    """Send a WhatsApp message using the MCP WhatsApp tool."""
    
    try:
        # Prepare WhatsApp parameters
        whatsapp_params = {
            "phone_number": recipient,
            "message": _format_whatsapp_content(content, user_context)
        }
        
        # Execute the WhatsApp tool
        result = await whatsapp_tool.ainvoke(whatsapp_params)
        
        # Check if the result indicates success
        return True  # Assume success for now
        
    except Exception as e:
        print(f"WhatsApp sending failed: {str(e)}")
        return False


def _format_email_content(content: str, user_context: Dict[str, Any]) -> str:
    """Format content for email with HTML styling."""
    
    user_name = user_context.get("user_id", "Valued Customer")
    
    html_content = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
            .content {{ padding: 20px; }}
            .footer {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 12px; color: #666; }}
            .highlight {{ background-color: #e3f2fd; padding: 10px; border-left: 4px solid #2196f3; margin: 10px 0; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>🎯 CatchUp - Il tuo assistente marketplace</h2>
        </div>
        
        <div class="content">
            <p>Ciao {user_name},</p>
            
            <div class="highlight">
                {content.replace('\n', '<br>')}
            </div>
            
            <p>Grazie per aver utilizzato CatchUp!</p>
        </div>
        
        <div class="footer">
            <p>Questo messaggio è stato generato automaticamente dal sistema CatchUp.<br>
            Per assistenza, rispondi a questa email o contattaci tramite WhatsApp.</p>
        </div>
    </body>
    </html>
    """
    
    return html_content


def _format_whatsapp_content(content: str, user_context: Dict[str, Any]) -> str:
    """Format content for WhatsApp with appropriate emojis and formatting."""
    
    formatted_content = f"🎯 *CatchUp Update*\n\n{content}\n\n"
    formatted_content += "Grazie per aver utilizzato CatchUp! 🚀"
    
    return formatted_content
