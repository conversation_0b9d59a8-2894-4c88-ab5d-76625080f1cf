from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from typing import Literal
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END ,START
from langgraph.prebuilt import create_react_agent

from bond_ai.state import PlanExecute
from bond_ai.models import Plan
from bond_ai.prompts import PLANNER_PROMPT
from bond_ai.tools import all_tools
import os
load_dotenv()

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Choose the LLM that will drive the agent
llm = ChatOpenAI(model="gpt-4-turbo-preview")
prompt_executor = "You are Bond AI Executor, an helpfull task executor. Your goal is to execute the task as accurately as possible."
agent_executor = create_react_agent(llm, all_tools, prompt=prompt_executor)


planner_prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
          PLANNER_PROMPT,
        ),
        ("placeholder", "{messages}"),
    ]
)
planner = planner_prompt | ChatOpenAI(
    model="gpt-4o", temperature=0
).with_structured_output(Plan)



async def plan_step(state: PlanExecute):
    plan = await planner.ainvoke({"messages": [("user", state["input"])]})
    return {"plan": plan.steps}



async def execute_step(state: PlanExecute):
    plan = state["plan"]
    plan_str = "\n".join(f"{i + 1}. {step}" for i, step in enumerate(plan))
    
    # Calculate current step number based on remaining plan length
    current_step = len(state.get("past_steps", [])) + 1
    task = plan[0]  # This should be the next task to execute
    
    task_formatted = f"""For the following plan:
{plan_str}\n\nYou are tasked with executing step {current_step}, {task}."""
    
    agent_response = await agent_executor.ainvoke(
        {"messages": [("user", task_formatted)]}
    )
    
    # Check if this was the last step
    remaining_plan = plan[1:]
    if not remaining_plan:
        # Last step - set response to end the workflow
        return {
            "past_steps": [(task, agent_response["messages"][-1].content)],
            "response": agent_response["messages"][-1].content,
            "plan": remaining_plan
        }
    else:
        # More steps remaining
        return {
            "past_steps": [(task, agent_response["messages"][-1].content)],
            "plan": remaining_plan
        }
    
    



def should_end(state: PlanExecute):
    if "response" in state and state["response"]:
        return "end"
    else:
        return "executor"

workflow = StateGraph(PlanExecute)

# Add the plan node
workflow.add_node("planner", plan_step)



# Add the execution step
#workflow.add_node("executor", execute_step)
#workflow.add_edge("planner","executor")


# workflow.add_conditional_edges(
#     "executor",
#     should_end,
#     {
#         "executor": "executor",
#         "end": END,
#     },
# )
workflow.add_edge(START, "planner")
workflow.add_edge("planner",END)
graph = workflow.compile()


# config = {"recursion_limit": 50}
# inputs = {"input": "Create the ICP for outbond.io and based on the ICP create the personas as well"}

# async def main():
#     async for event in graph.astream(inputs, config=config):
#         for k, v in event.items():
#             if k != "__end__":
#                 print(v)

# # Run the async function
# import asyncio
# asyncio.run(main())
