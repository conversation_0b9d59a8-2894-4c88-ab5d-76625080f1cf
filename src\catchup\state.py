
from typing import TypedDict, Annotated, Optional, List, Dict, Any, Union
from typing_extensions import Required
from langchain_core.messages import AnyMessage
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

# class ConversationPhase(Enum):
#     """Track conversation phase for better context management."""
#     GREETING = "greeting"
#     INTENT_DISCOVERY = "intent_discovery"
#     INFORMATION_GATHERING = "information_gathering"
#     TOOL_EXECUTION = "tool_execution"
#     RESULT_PRESENTATION = "result_presentation"
#     FOLLOW_UP = "follow_up"
#     CLOSING = "closing"

# class UserContext(TypedDict, total=False):
#     """Enhanced user context with preferences and history."""
#     user_id: Required[str]
#     email_address: Optional[str]
#     location: Optional[Dict[str, float]]  # {"latitude": 45.4666, "longitude": 9.1832}
#     preferences: Optional[Dict[str, Any]]  # User preferences (language, categories, etc.)
#     interaction_history: Optional[Dict[str, Any]]  # Previous interaction patterns
#     current_session_start: Optional[str]  # ISO timestamp

# class ConversationMetrics(TypedDict, total=False):
#     """Track conversation quality and performance."""
#     message_count: int
#     tool_calls_count: int
#     successful_tool_calls: int
#     failed_tool_calls: int
#     conversation_start: str  # ISO timestamp
#     last_activity: str  # ISO timestamp
#     estimated_tokens_used: int
#     user_satisfaction_indicators: List[str]  # ["quick_response", "tool_success", etc.]

# class SessionConfig(TypedDict, total=False):
#     """Session-specific configuration."""
#     session_id: Required[str]
#     memory_budget: int  # Token budget for this session
#     max_tool_calls_per_turn: int
#     conversation_timeout: int  # Minutes
#     preferred_response_style: str  # "concise", "detailed", "conversational"
#     enable_proactive_suggestions: bool

def enhanced_memory_reducer(existing: List[AnyMessage], new: List[AnyMessage]) -> List[AnyMessage]:
    """Enhanced reducer with intelligent message prioritization."""
    if not existing:
        return new
    
    if not new:
        return existing
    
    # Combine messages
    combined = existing + new
    
    # Get memory budget from the last message's metadata if available
    memory_budget = 15  # Default
    if new and hasattr(new[-1], 'additional_kwargs'):
        memory_budget = new[-1].additional_kwargs.get('memory_budget', 15)
    
    if len(combined) <= memory_budget:
        return combined
    
    # Intelligent trimming: keep system messages, recent messages, and important tool results
    important_messages = []
    recent_messages = []
    
    # Always keep the last few messages
    recent_count = min(5, memory_budget // 2)
    recent_messages = combined[-recent_count:]
    
    # Keep important messages from earlier in conversation
    remaining_budget = memory_budget - len(recent_messages)
    for msg in combined[:-recent_count]:
        if remaining_budget <= 0:
            break
            
        # Keep system messages
        if hasattr(msg, 'type') and msg.type == 'system':
            important_messages.append(msg)
            remaining_budget -= 1
        # Keep successful tool results
        elif hasattr(msg, 'type') and msg.type == 'tool' and 'error' not in msg.content.lower():
            important_messages.append(msg)
            remaining_budget -= 1
        # Keep messages with user preferences or important context
        elif hasattr(msg, 'content') and any(keyword in msg.content.lower() 
                                           for keyword in ['my name is', 'i prefer', 'i like', 'i need']):
            important_messages.append(msg)
            remaining_budget -= 1
    
    # Combine important + recent messages, maintaining chronological order
    result = important_messages + recent_messages
    
    # Remove duplicates while preserving order
    seen = set()
    deduplicated = []
    for msg in result:
        msg_id = id(msg)
        if msg_id not in seen:
            seen.add(msg_id)
            deduplicated.append(msg)
    
    return deduplicated

# class CatchUpState(TypedDict):
#     """Enhanced state with better organization and context management."""
    
#     # Core conversation
#     messages: Annotated[List[AnyMessage], enhanced_memory_reducer]
    
#     # User and session context
#     user_context: UserContext
#     session_config: SessionConfig
    
#     # Conversation tracking
#     current_phase: ConversationPhase
#     conversation_metrics: ConversationMetrics
    
#     # Intent and routing context
#     detected_intent: Optional[str]
#     intent_confidence: Optional[float]
#     required_tool_categories: Optional[List[str]]
    
#     # Tool execution context
#     pending_tool_calls: Optional[List[Dict[str, Any]]]
#     tool_execution_history: Optional[List[Dict[str, Any]]]
    
#     # Response optimization
#     response_context: Optional[Dict[str, Any]]  # Context for response generation
#     user_feedback: Optional[Dict[str, Any]]  # Implicit feedback signals
#     plan: str  # Add this field

# Backward compatibility: keep original CatchUpState for existing code
class CatchUpState(TypedDict):
    """Original state for backward compatibility."""
    messages: Annotated[List[AnyMessage], enhanced_memory_reducer]
    query: Optional[str]
    session_id: Required[str]
    user_id: Required[str]
    latitude: Optional[str]
    longitude: Optional[str]
    memory_lenght: Optional[str]
    email_address: Optional[str]
    isChat: Optional[bool]

# def migrate_to_enhanced_state(old_state: CatchUpState) -> CatchUpState:
#     """Migrate old state format to enhanced state format."""
    
#     # Extract location
#     location = None
#     if old_state.get('latitude') and old_state.get('longitude'):
#         location = {
#             "latitude": float(old_state['latitude']),
#             "longitude": float(old_state['longitude'])
#         }
    
#     # Create user context
#     user_context = UserContext(
#         user_id=old_state['user_id'],
#         email_address=old_state.get('email_address'),
#         location=location,
#         current_session_start=datetime.now().isoformat()
#     )
    
#     # Create session config
#     memory_budget = 15
#     if old_state.get('memory_lenght'):
#         try:
#             memory_budget = int(old_state['memory_lenght'])
#         except (ValueError, TypeError):
#             memory_budget = 15
    
#     session_config = SessionConfig(
#         session_id=old_state['session_id'],
#         memory_budget=memory_budget,
#         max_tool_calls_per_turn=3,
#         conversation_timeout=30,
#         preferred_response_style="conversational",
#         enable_proactive_suggestions=True
#     )
    
#     # Create conversation metrics
#     conversation_metrics = ConversationMetrics(
#         message_count=len(old_state.get('messages', [])),
#         tool_calls_count=0,
#         successful_tool_calls=0,
#         failed_tool_calls=0,
#         conversation_start=datetime.now().isoformat(),
#         last_activity=datetime.now().isoformat(),
#         estimated_tokens_used=0,
#         user_satisfaction_indicators=[]
#     )
    
#     # Determine current phase based on messages
#     current_phase = ConversationPhase.GREETING
#     messages = old_state.get('messages', [])
#     if messages:
#         if len(messages) > 2:
#             current_phase = ConversationPhase.INFORMATION_GATHERING
#         elif len(messages) > 1:
#             current_phase = ConversationPhase.INTENT_DISCOVERY
    
#     return EnhancedCatchUpState(
#         messages=messages,
#         user_context=user_context,
#         session_config=session_config,
#         current_phase=current_phase,
#         conversation_metrics=conversation_metrics,
#         detected_intent=None,
#         intent_confidence=None,
#         required_tool_categories=None,
#         pending_tool_calls=None,
#         tool_execution_history=None,
#         response_context=None,
#         user_feedback=None
#     )

# def update_conversation_metrics(state: CatchUpState, 
#                               tool_calls: int = 0, 
#                               successful_tools: int = 0, 
#                               failed_tools: int = 0,
#                               estimated_tokens: int = 0) -> CatchUpState:
#     """Update conversation metrics."""
    
#     metrics = state['conversation_metrics'].copy()
#     metrics['message_count'] = len(state['messages'])
#     metrics['tool_calls_count'] += tool_calls
#     metrics['successful_tool_calls'] += successful_tools
#     metrics['failed_tool_calls'] += failed_tools
#     metrics['last_activity'] = datetime.now().isoformat()
#     metrics['estimated_tokens_used'] += estimated_tokens
    
#     # Update satisfaction indicators
#     if successful_tools > 0:
#         if 'tool_success' not in metrics['user_satisfaction_indicators']:
#             metrics['user_satisfaction_indicators'].append('tool_success')
    
#     if failed_tools == 0 and tool_calls > 0:
#         if 'reliable_tools' not in metrics['user_satisfaction_indicators']:
#             metrics['user_satisfaction_indicators'].append('reliable_tools')
    
#     state['conversation_metrics'] = metrics
#     return state
