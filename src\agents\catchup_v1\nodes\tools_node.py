"""Tools node for CatchUp v1 agent."""

from __future__ import annotations

import asyncio
from typing import Any, Dict
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import ToolMessage, AIMessage
from langgraph.prebuilt import ToolNode
from langgraph.config import get_stream_writer

from agents.catchup_v1.state import CatchUpV1State
from agents.catchup_v1.configuration import CatchUpV1Configuration
from agents.catchup_v1.tools.marketplace import get_enhanced_marketplace_tools
from agents.catchup_v1.tools.planning import create_plan, update_task_status, get_current_plan
from agents.catchup_v1.tools.communication import send_progress_update, send_plan_summary, stream_status_update


async def tools_node(state: CatchUpV1State, config: RunnableConfig) -> Dict[str, Any]:
    """Enhanced tools node that executes tools with progress tracking and error handling.
    
    This node:
    1. Executes tool calls from the model
    2. Tracks tool execution in history
    3. Handles errors gracefully with retry logic
    4. Updates progress metrics
    5. Streams progress updates for long-running operations
    """
    
    configuration = CatchUpV1Configuration.from_runnable_config(config)
    messages = state.get("messages", [])
    session_config = state.get("session_config", {})
    retry_count = state.get("retry_count", 0)
    
    if not messages:
        return {"messages": [AIMessage(content="No messages to process for tool execution.")]}
    
    # Get the last message with tool calls
    last_message = messages[-1]
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": [AIMessage(content="No tool calls found in the last message.")]}
    
    # Get all available tools
    marketplace_tools = await get_enhanced_marketplace_tools()
    planning_tools = [create_plan, update_task_status, get_current_plan]
    communication_tools = [send_progress_update, send_plan_summary, stream_status_update]
    
    all_tools = marketplace_tools + planning_tools + communication_tools
    
    # Create tool node
    tool_node = ToolNode(all_tools)
    
    # Track execution start
    execution_start = datetime.now()
    
    # Stream status update if enabled
    if session_config.get("streaming_enabled", True):
        try:
            writer = get_stream_writer()
            if writer:
                writer.write({
                    "type": "tool_execution_start",
                    "tool_calls": [call.get("name", "unknown") for call in last_message.tool_calls],
                    "timestamp": execution_start.isoformat()
                })
        except Exception:
            pass  # Don't fail if streaming fails
    
    try:
        # Execute tools with timeout
        timeout = session_config.get("tool_timeout", 30)
        
        result = await asyncio.wait_for(
            tool_node.ainvoke(state, config),
            timeout=timeout
        )
        
        # Calculate execution time
        execution_time = (datetime.now() - execution_start).total_seconds()
        
        # Update tool execution history
        tool_execution_entry = {
            "timestamp": execution_start.isoformat(),
            "tools_called": [call.get("name", "unknown") for call in last_message.tool_calls],
            "execution_time": execution_time,
            "success": True,
            "retry_count": retry_count
        }
        
        current_history = state.get("tool_execution_history", [])
        updated_history = current_history + [tool_execution_entry]
        
        # Update progress metrics
        current_metrics = state.get("progress_metrics", {})
        tools_used = current_metrics.get("tools_used", [])
        
        for call in last_message.tool_calls:
            tool_name = call.get("name", "unknown")
            if tool_name not in tools_used:
                tools_used.append(tool_name)
        
        updated_metrics = {
            **current_metrics,
            "tools_used": tools_used
        }
        
        # Stream completion status
        if session_config.get("streaming_enabled", True):
            try:
                writer = get_stream_writer()
                if writer:
                    writer.write({
                        "type": "tool_execution_complete",
                        "execution_time": execution_time,
                        "success": True,
                        "timestamp": datetime.now().isoformat()
                    })
            except Exception:
                pass
        
        # Merge results with state updates
        state_updates = {
            "tool_execution_history": updated_history,
            "progress_metrics": updated_metrics,
            "retry_count": 0  # Reset retry count on success
        }
        
        # Merge with tool node results
        if isinstance(result, dict):
            state_updates.update(result)
        else:
            state_updates["messages"] = result.get("messages", [])
        
        return state_updates
        
    except asyncio.TimeoutError:
        # Handle timeout
        error_message = f"Tool execution timed out after {timeout} seconds."
        
        return await _handle_tool_error(
            error_message,
            "timeout",
            state,
            configuration,
            execution_start
        )
        
    except Exception as e:
        # Handle other errors
        error_message = f"Tool execution failed: {str(e)}"
        
        return await _handle_tool_error(
            error_message,
            "execution_error",
            state,
            configuration,
            execution_start
        )


async def _handle_tool_error(
    error_message: str,
    error_type: str,
    state: CatchUpV1State,
    configuration: CatchUpV1Configuration,
    execution_start: datetime
) -> Dict[str, Any]:
    """Handle tool execution errors with retry logic and graceful degradation."""
    
    retry_count = state.get("retry_count", 0)
    max_retries = configuration.max_tool_retries
    session_config = state.get("session_config", {})
    
    # Calculate execution time
    execution_time = (datetime.now() - execution_start).total_seconds()
    
    # Update tool execution history
    tool_execution_entry = {
        "timestamp": execution_start.isoformat(),
        "tools_called": ["unknown"],  # We don't have access to the specific tools that failed
        "execution_time": execution_time,
        "success": False,
        "error_type": error_type,
        "error_message": error_message,
        "retry_count": retry_count
    }
    
    current_history = state.get("tool_execution_history", [])
    updated_history = current_history + [tool_execution_entry]
    
    # Stream error status
    if session_config.get("streaming_enabled", True):
        try:
            writer = get_stream_writer()
            if writer:
                writer.write({
                    "type": "tool_execution_error",
                    "error_type": error_type,
                    "error_message": error_message,
                    "retry_count": retry_count,
                    "max_retries": max_retries,
                    "timestamp": datetime.now().isoformat()
                })
        except Exception:
            pass
    
    # Determine if we should retry
    if retry_count < max_retries and configuration.error_recovery_enabled:
        return {
            "tool_execution_history": updated_history,
            "retry_count": retry_count + 1,
            "error_context": {
                "last_error": error_message,
                "error_type": error_type,
                "retry_attempt": retry_count + 1
            },
            "messages": [AIMessage(content=f"⚠️ Tool execution encountered an issue. Retrying... (attempt {retry_count + 1}/{max_retries})")]
        }
    else:
        # Max retries reached or recovery disabled
        recovery_message = f"❌ {error_message}"
        
        if retry_count >= max_retries:
            recovery_message += f" Maximum retry attempts ({max_retries}) reached."
        
        recovery_message += " I'll continue with the available information."
        
        return {
            "tool_execution_history": updated_history,
            "retry_count": 0,  # Reset for next operation
            "error_context": {
                "last_error": error_message,
                "error_type": error_type,
                "max_retries_reached": retry_count >= max_retries
            },
            "messages": [AIMessage(content=recovery_message)]
        }


# Human-in-the-loop tools that require confirmation
HIL_TOOLS = {
    "whatsapps_sent_tool",
    "sent_email_to_users",
    "create_booking"
}


def requires_human_confirmation(tool_name: str) -> bool:
    """Check if a tool requires human confirmation before execution."""
    return tool_name in HIL_TOOLS
