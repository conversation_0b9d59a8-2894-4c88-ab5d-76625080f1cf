from typing import List
from pydantic import BaseModel, Field


class Task(BaseModel):
    """Task to be executed"""
    order: int = Field(description="order of the task")
    task: str = Field(description="task to be executed")
    tool: str = Field(description="tool to be used")
    why: str = Field(description="reason for the task")

class Plan(BaseModel):
    """Plan to follow in future"""
    objective: str = Field(description="objective of the plan")
    steps: List[Task] = Field(
        description="different steps to follow, should be in sorted order"
    )