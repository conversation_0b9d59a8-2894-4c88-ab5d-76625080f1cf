"""State definitions.

State is the interface between the graph and end user as well as the
data model used internally by the graph.
"""

import operator
from dataclasses import dataclass, field
from datetime import datetime
from typing import Annotated, Any, List, Optional

from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages


@dataclass(kw_only=True)
class Citation:
    """Represents a source citation with metadata."""
    
    url: str
    "The source URL"
    
    title: Optional[str] = None
    "The title of the source (if available)"
    
    content_snippet: Optional[str] = None  
    "A brief snippet of the source content"
    
    access_date: str = field(default_factory=lambda: datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"))
    "The date and time the source was accessed"
    
    source_type: str = "web"
    "The type of source (web, search, etc.)"


@dataclass(kw_only=True)
class InputState:
    """Input state defines the interface between the graph and the user (external API)."""

    query: str
    "The topic for which the agent is tasked to gather information."

    extraction_schema: dict[str, Any]
    "The simple extraction schema in format: {'format': 'json', 'fields': {'field_name': 'data_type'}} or {'format': 'text'}."

    info: Optional[dict[str, Any] | str] = field(default=None)
    "The info state tracks the current extracted data for the given topic, conforming to the provided schema. For JSON format, this is a dict; for text format, this is a string extracted from the text_response field. This is primarily populated by the agent."


@dataclass(kw_only=True)
class State(InputState):
    """Graph's State  """

    messages: Annotated[List[BaseMessage], add_messages] = field(default_factory=list)
    """ Messages track the primary execution state of the agent. """

    loop_step: Annotated[int, operator.add] = field(default=0)
    
    citations: Annotated[List[Citation], lambda x, y: x + y] = field(default_factory=list)
    """
    Citations track all sources used during the research process.
    
    This list accumulates citations from all tools (search, scrape) and provides
    source attribution for the final response. Each citation includes:
    - Source URL
    - Title (if available)  
    - Content snippet
    - Access date
    - Source type
    
    Uses a custom reducer that concatenates citation lists to ensure all sources
    are preserved throughout the research process.
    """

    # Feel free to add additional attributes to your state as needed.
    # Common examples include retrieved documents, extracted entities, API connections, etc.


@dataclass(kw_only=True)
class OutputState:
    """The response object for the end user.

    This class defines the structure of the output that will be provided
    to the user after the graph's execution is complete.
    """

    info: dict[str, Any] | str
    """
    The extracted and processed information based on the user's query and the graph's execution.
    For JSON format schemas, this is a dictionary containing structured data.
    For text format schemas, this is a string containing the text response (extracted from the text_response field).
    This is the primary output of the enrichment process.
    """
    
    citations: List[Citation] = field(default_factory=list)
    """
    Source citations for all information in the response.
    
    Provides full attribution for all facts, data, and insights included in the
    response. Each citation includes source URL, title, access date, and content snippet.
    This ensures transparency and allows users to verify information sources.
    """
