"""Executor node for CatchUp v1 agent."""

from __future__ import annotations

from typing import Dict, Any, Optional
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage

from agents.catchup_v1.state import CatchUpV1State, Task
from agents.catchup_v1.configuration import CatchUpV1Configuration


async def executor_node(state: CatchUpV1State, config: RunnableConfig) -> Dict[str, Any]:
    """Executor node that manages task execution and progress tracking.
    
    This node:
    1. Identifies the next task to execute based on dependencies
    2. Updates task status to in_progress
    3. Manages the execution flow
    4. Handles task completion and failure
    5. Sends progress updates to users
    """
    
    configuration = CatchUpV1Configuration.from_runnable_config(config)
    current_plan = state.get("current_plan", [])
    active_task_id = state.get("active_task_id")
    progress_metrics = state.get("progress_metrics", {})
    
    if not current_plan:
        return {
            "messages": [AIMessage(content="No plan exists to execute. Please create a plan first.")],
            "conversation_phase": {
                "phase": "understanding",
                "confidence": 0.8,
                "context": {"no_plan_to_execute": True}
            }
        }
    
    # Find the next task to execute
    next_task = _find_next_executable_task(current_plan, active_task_id)
    
    if not next_task:
        # Check if all tasks are completed
        completed_tasks = [task for task in current_plan if task["status"] == "completed"]
        failed_tasks = [task for task in current_plan if task["status"] == "failed"]
        total_tasks = len(current_plan)
        
        if len(completed_tasks) + len(failed_tasks) == total_tasks:
            # All tasks are done
            success_rate = len(completed_tasks) / total_tasks * 100 if total_tasks > 0 else 0
            
            completion_message = f"🎉 Plan execution completed!\n\n"
            completion_message += f"📊 Results: {len(completed_tasks)}/{total_tasks} tasks completed ({success_rate:.1f}% success rate)"
            
            if failed_tasks:
                completion_message += f"\n❌ {len(failed_tasks)} tasks failed"
            
            return {
                "conversation_phase": {
                    "phase": "closing",
                    "confidence": 1.0,
                    "context": {
                        "execution_completed": True,
                        "success_rate": success_rate,
                        "total_tasks": total_tasks,
                        "completed_tasks": len(completed_tasks),
                        "failed_tasks": len(failed_tasks)
                    }
                },
                "messages": [AIMessage(content=completion_message)]
            }
        else:
            # Some tasks are still pending but can't be executed (dependency issues)
            pending_tasks = [task for task in current_plan if task["status"] == "pending"]
            
            return {
                "messages": [AIMessage(content=f"⏸️ Execution paused. {len(pending_tasks)} tasks are waiting for dependencies or manual intervention.")],
                "conversation_phase": {
                    "phase": "executing",
                    "confidence": 0.7,
                    "context": {"execution_paused": True, "pending_tasks": len(pending_tasks)}
                }
            }
    
    # Start executing the next task
    current_time = datetime.now().isoformat()
    
    # Update the task status to in_progress
    updated_plan = []
    for task in current_plan:
        if task["id"] == next_task["id"]:
            updated_task = {**task}
            updated_task["status"] = "in_progress"
            updated_task["started_at"] = current_time
            updated_plan.append(updated_task)
        else:
            updated_plan.append(task)
    
    # Update conversation phase
    conversation_phase = {
        "phase": "executing",
        "confidence": 0.9,
        "context": {
            "current_task_id": next_task["id"],
            "current_task_content": next_task["content"],
            "execution_started_at": current_time
        }
    }
    
    # Create execution message
    priority_emoji = {"low": "🔵", "medium": "🟡", "high": "🟠", "critical": "🔴"}.get(next_task["priority"], "⚪")
    tools_info = f" (using: {', '.join(next_task.get('tools_required', []))})" if next_task.get("tools_required") else ""
    
    execution_message = f"🔄 Executing task: {priority_emoji} {next_task['content']}{tools_info}"
    
    # Calculate progress
    completed_count = len([task for task in current_plan if task["status"] == "completed"])
    total_count = len(current_plan)
    progress_percentage = (completed_count / total_count * 100) if total_count > 0 else 0
    
    if progress_percentage > 0:
        execution_message += f"\n📈 Progress: {completed_count}/{total_count} tasks completed ({progress_percentage:.1f}%)"
    
    return {
        "current_plan": updated_plan,
        "active_task_id": next_task["id"],
        "conversation_phase": conversation_phase,
        "messages": [AIMessage(content=execution_message)]
    }


def _find_next_executable_task(current_plan: list[Task], active_task_id: Optional[str]) -> Optional[Task]:
    """Find the next task that can be executed based on dependencies.
    
    Args:
        current_plan: List of tasks in the current plan
        active_task_id: ID of currently active task (if any)
    
    Returns:
        Next executable task or None if no task can be executed
    """
    
    # If there's an active task that's still in progress, return None
    if active_task_id:
        active_task = next((task for task in current_plan if task["id"] == active_task_id), None)
        if active_task and active_task["status"] == "in_progress":
            return None
    
    # Find pending tasks that have all dependencies completed
    for task in current_plan:
        if task["status"] != "pending":
            continue
        
        # Check if all dependencies are completed
        dependencies_met = True
        for dep_id in task.get("dependencies", []):
            dep_task = next((t for t in current_plan if t["id"] == dep_id), None)
            if not dep_task or dep_task["status"] != "completed":
                dependencies_met = False
                break
        
        if dependencies_met:
            return task
    
    return None


async def complete_task(
    task_id: str,
    state: CatchUpV1State,
    success: bool,
    result_message: str,
    actual_duration: Optional[int] = None,
    error_message: Optional[str] = None,
) -> Dict[str, Any]:
    """Complete a task and update the state accordingly.
    
    Args:
        task_id: ID of the task to complete
        success: Whether the task completed successfully
        result_message: Message describing the result
        actual_duration: Actual duration in seconds
        error_message: Error message if task failed
        state: Current state
    
    Returns:
        State updates for task completion
    """
    
    current_plan = state.get("current_plan", [])
    current_time = datetime.now().isoformat()
    
    # Update the task
    updated_plan = []
    task_found = False
    
    for task in current_plan:
        if task["id"] == task_id:
            task_found = True
            updated_task = {**task}
            updated_task["status"] = "completed" if success else "failed"
            updated_task["completed_at"] = current_time
            
            if actual_duration:
                updated_task["actual_duration"] = actual_duration
            if error_message:
                updated_task["error_message"] = error_message
            
            updated_plan.append(updated_task)
        else:
            updated_plan.append(task)
    
    if not task_found:
        return {"messages": [AIMessage(content=f"Error: Task {task_id} not found.")]}
    
    # Update progress metrics
    current_metrics = state.get("progress_metrics", {})
    completed_count = len([task for task in updated_plan if task["status"] == "completed"])
    failed_count = len([task for task in updated_plan if task["status"] == "failed"])
    
    updated_metrics = {
        **current_metrics,
        "completed_tasks": completed_count,
        "failed_tasks": failed_count
    }
    
    # Calculate average task duration
    completed_tasks_with_duration = [
        task for task in updated_plan 
        if task["status"] == "completed" and task.get("actual_duration")
    ]
    if completed_tasks_with_duration:
        avg_duration = sum(task["actual_duration"] for task in completed_tasks_with_duration) / len(completed_tasks_with_duration)
        updated_metrics["average_task_duration"] = avg_duration
    
    # Create completion message
    status_emoji = "✅" if success else "❌"
    completion_message = f"{status_emoji} Task completed: {result_message}"
    
    return {
        "current_plan": updated_plan,
        "progress_metrics": updated_metrics,
        "active_task_id": None,  # Clear active task
        "messages": [AIMessage(content=completion_message)]
    }
