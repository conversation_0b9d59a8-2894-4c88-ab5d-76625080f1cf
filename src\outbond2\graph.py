"""Define a data enrichment agent.

Works with a chat model with tool calling support.
"""

from langgraph.graph import StateGraph

from outbond2.configuration import Configuration
from outbond2.nodes import sdr_research_orchestrator,sdr_quality_validator,create_tools_node,route_after_agent,route_after_checker
from outbond2.state import State



# Create the graph
workflow = StateGraph(State, config_schema=Configuration)
workflow.add_node(sdr_research_orchestrator)
workflow.add_node(sdr_quality_validator)
workflow.add_node("tools", create_tools_node())
workflow.add_edge("__start__", "sdr_research_orchestrator")
workflow.add_conditional_edges("sdr_research_orchestrator", route_after_agent)
workflow.add_edge("tools", "sdr_research_orchestrator")
workflow.add_conditional_edges("sdr_quality_validator", route_after_checker)

graph = workflow.compile()
graph.name = "OutbondSearchAgent2"
