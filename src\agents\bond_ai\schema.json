{"type": "object", "properties": {"objective": {"type": "string", "description": "High-level goal for the prospecting plan"}, "tasks": {"type": "array", "description": "Ordered list of tasks to achieve the objective", "items": {"type": "object", "properties": {"order": {"type": "integer", "minimum": 1, "description": "The sequential order of the task"}, "task": {"type": "string", "description": "A short description of the action to take"}, "tool": {"type": "string", "description": "The tool or function to execute the task"}, "why": {"type": "string", "description": "The reason or intended outcome for the task"}}, "required": ["order", "task", "tool", "why"], "additionalProperties": false}, "minItems": 1}}, "required": ["objective", "tasks"], "additionalProperties": false}