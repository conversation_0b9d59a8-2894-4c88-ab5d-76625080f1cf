# """Logging configuration for the outbound search agent."""

import configparser
import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path
from typing import Optional

# Global logger instance - automatically configured
_global_logger = None
_is_configured = False


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_dir: str = "logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_logging: bool = False,
    file_logging: bool = True
) -> None:
    """Setup logging configuration with file and console handlers.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Custom log file name (optional)
        log_dir: Directory to store log files
        max_file_size: Maximum size of each log file in bytes
        backup_count: Number of backup files to keep
        console_logging: Whether to also log to console
        file_logging: Whether to enable file logging
    """
    
    # Create logs directory if it doesn't exist and file logging is enabled
    if file_logging:
        log_path = Path(log_dir)
        log_path.mkdir(exist_ok=True)

        # Generate log file name if not provided
        if log_file is None:
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = f"outbound_agent_{timestamp}.log"

        log_file_path = log_path / log_file
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler with rotation (only if file logging is enabled)
    if file_logging:
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Console handler (optional)
    if console_logging:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)  # Console shows INFO and above
        console_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

    # Log the setup completion
    logger = logging.getLogger(__name__)
    if file_logging:
        logger.info(f"Logging configured - File: {log_file_path}, Level: {log_level}")
        logger.info(f"Log rotation: {max_file_size // (1024*1024)}MB max, {backup_count} backups")
    else:
        logger.info(f"Logging configured - Console only, Level: {log_level}")
    logger.info(f"Console logging: {'Enabled' if console_logging else 'Disabled'}")
    logger.info(f"File logging: {'Enabled' if file_logging else 'Disabled'}")



def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name.

    Args:
        name: Logger name (typically __name__)

    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def _auto_configure_logging():
    """Automatically configure logging with hierarchical priority:
    1. Config file (logging.conf) if exists
    2. Environment variables if available
    3. Default configuration as fallback
    """
    global _is_configured
    if not _is_configured:
        try:
            # Priority 1: Try config file first
            if os.path.exists("logging.conf"):
                configure_from_file("logging.conf")
            else:
                # Priority 2: Try environment variables
                configure_from_env()
        except Exception:
            # Priority 3: Fallback to default configuration
            setup_logging(
                log_level="INFO",
                console_logging=True,
                file_logging=True
            )
        _is_configured = True


def get_global_logger() -> logging.Logger:
    """Get a pre-configured global logger instance.

    This function automatically configures logging on first call and returns
    a logger that can be used throughout the application without needing
    to import logging_config in every file.

    Returns:
        Logger instance ready to use
    """
    global _global_logger
    if _global_logger is None:
        _auto_configure_logging()
        _global_logger = logging.getLogger('global')
    return _global_logger


# Environment-based configuration
def configure_from_env() -> None:
    """Configure logging based on environment variables.

    Environment variables:
        LOG_LEVEL: Logging level (default: INFO)
        LOG_FILE: Custom log file name
        LOG_DIR: Log directory (default: logs)
        CONSOLE_LOGGING: Enable console logging (default: true)
        FILE_LOGGING: Enable file logging (default: true)
    """
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file = os.getenv('LOG_FILE')
    log_dir = os.getenv('LOG_DIR', 'logs')
    console_logging = os.getenv('CONSOLE_LOGGING', 'true').lower() == 'true'
    file_logging = os.getenv('FILE_LOGGING', 'true').lower() == 'true'

    setup_logging(
        log_level=log_level,
        log_file=log_file,
        log_dir=log_dir,
        console_logging=console_logging,
        file_logging=file_logging
    )


def configure_from_file(config_file: str = "logging.conf") -> None:
    """Configure logging based on configuration file.

    Args:
        config_file: Path to configuration file
    """

    if not os.path.exists(config_file):
        print(f"Warning: Config file {config_file} not found, using defaults")
        configure_from_env()
        return

    config = configparser.ConfigParser()
    config.read(config_file)

    # Read LOG_SETTINGS section
    log_level = config.get('LOG_SETTINGS', 'LOG_LEVEL', fallback='INFO')
    log_dir = config.get('LOG_SETTINGS', 'LOG_DIR', fallback='logs')
    console_logging = config.getboolean('LOG_SETTINGS', 'CONSOLE_LOGGING', fallback=True)
    file_logging = config.getboolean('LOG_SETTINGS', 'FILE_LOGGING', fallback=True)
    max_file_size_mb = config.getint('LOG_SETTINGS', 'MAX_FILE_SIZE_MB', fallback=10)
    backup_count = config.getint('LOG_SETTINGS', 'BACKUP_COUNT', fallback=5)
    log_file = config.get('LOG_SETTINGS', 'LOG_FILE', fallback=None)

    setup_logging(
        log_level=log_level,
        log_file=log_file,
        log_dir=log_dir,
        max_file_size=max_file_size_mb * 1024 * 1024,  # Convert MB to bytes
        backup_count=backup_count,
        console_logging=console_logging,
        file_logging=file_logging
    )



def disable_logging():
    """Disable all logging."""
    logging.disable(logging.CRITICAL)


def setup_null_logging():
    """Setup null logging (no output)."""
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.addHandler(logging.NullHandler())
    root_logger.setLevel(logging.CRITICAL)








