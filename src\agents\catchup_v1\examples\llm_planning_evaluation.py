#!/usr/bin/env python3
"""
Example: LLM-Based Planning Evaluation

This example demonstrates the new intelligent planning evaluation system that uses
a fast LLM to determine whether a user request requires multi-step planning or can
be handled directly by the main model.

Key Benefits:
1. More accurate than keyword-based heuristics
2. Handles multilingual requests (Italian, English, etc.)
3. Understands context and complexity better
4. Fast evaluation using lightweight models
5. Graceful fallback to heuristics if LLM fails

The system uses OpenAI's GPT-4o-mini for fast, cost-effective evaluation.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from agents.catchup_v1.graph import should_create_plan
from agents.catchup_v1.state import create_initial_state
from langchain_core.messages import HumanMessage


async def demonstrate_planning_evaluation():
    """Demonstrate the LLM-based planning evaluation with various examples."""
    
    print("🤖 LLM-Based Planning Evaluation Demo")
    print("=" * 50)
    
    # Create a basic state
    state = create_initial_state(
        user_id="demo_user",
        session_id="demo_session",
        email_address="<EMAIL>"
    )
    
    # Comprehensive test cases
    test_cases = [
        # Simple requests that should go DIRECT to model
        {
            "message": "Hello!",
            "expected": "call_model",
            "category": "Greeting"
        },
        {
            "message": "How are you today?",
            "expected": "call_model", 
            "category": "Conversational"
        },
        {
            "message": "What's the weather like?",
            "expected": "call_model",
            "category": "Simple Question"
        },
        {
            "message": "Tell me about your services",
            "expected": "call_model",
            "category": "Information Request"
        },
        
        # Complex requests that should require PLANNING
        {
            "message": "Find me a restaurant and book a table for 4 people tonight",
            "expected": "planner",
            "category": "Multi-step Booking"
        },
        {
            "message": "Search for deals in Milan and send me the best ones",
            "expected": "planner", 
            "category": "Search + Communication"
        },
        {
            "message": "Book a hotel with breakfast included and send confirmation",
            "expected": "planner",
            "category": "Booking + Notification"
        },
        {
            "message": "Find aperitivo deals with 20% discount and create a list",
            "expected": "planner",
            "category": "Search + Organization"
        },
        
        # Italian requests (multilingual support)
        {
            "message": "Ciao, come stai?",
            "expected": "call_model",
            "category": "Italian Greeting"
        },
        {
            "message": "Cerca un aperitivo a Milano con sconto di almeno il 20%",
            "expected": "planner",
            "category": "Italian Search Request"
        },
        {
            "message": "Prenota un tavolo e inviami la conferma",
            "expected": "planner",
            "category": "Italian Booking + Notification"
        },
        
        # Edge cases
        {
            "message": "Can you help me find something?",
            "expected": "call_model",
            "category": "Vague Request"
        },
        {
            "message": "I need to book multiple things: hotel, restaurant, and taxi",
            "expected": "planner",
            "category": "Multiple Complex Tasks"
        }
    ]
    
    # Group by category for better presentation
    categories = {}
    for test_case in test_cases:
        category = test_case["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append(test_case)
    
    results = []
    
    for category, cases in categories.items():
        print(f"\n📂 {category}")
        print("-" * 30)
        
        for case in cases:
            # Add the test message to state
            test_state = {
                **state,
                "messages": [HumanMessage(content=case["message"])]
            }
            
            try:
                # Call the planning evaluation function
                result = await should_create_plan(test_state)
                
                # Determine if this was correct
                success = result == case["expected"]
                status = "✅" if success else "❌"
                
                print(f"{status} '{case['message'][:50]}{'...' if len(case['message']) > 50 else ''}'")
                print(f"   → {result} (expected: {case['expected']})")
                
                results.append({
                    "category": category,
                    "message": case["message"],
                    "expected": case["expected"],
                    "actual": result,
                    "success": success
                })
                
            except Exception as e:
                print(f"❌ ERROR: {e}")
                results.append({
                    "category": category,
                    "message": case["message"],
                    "expected": case["expected"],
                    "actual": f"ERROR: {e}",
                    "success": False
                })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 EVALUATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    print(f"Overall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    # Category breakdown
    category_stats = {}
    for result in results:
        cat = result["category"]
        if cat not in category_stats:
            category_stats[cat] = {"passed": 0, "total": 0}
        category_stats[cat]["total"] += 1
        if result["success"]:
            category_stats[cat]["passed"] += 1
    
    print("\nBy Category:")
    for category, stats in category_stats.items():
        rate = stats["passed"] / stats["total"] * 100
        print(f"  {category}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
    
    if passed < total:
        print(f"\n❌ Failed Cases:")
        for r in results:
            if not r["success"]:
                print(f"  - {r['category']}: '{r['message'][:40]}...'")
                print(f"    Expected: {r['expected']}, Got: {r['actual']}")


if __name__ == "__main__":
    asyncio.run(demonstrate_planning_evaluation())
