"""Main graph for CatchUp v1 - Modern AI Agentic Design."""

from __future__ import annotations

from typing import Literal, Dict, Any
from langchain_core.messages import AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from agents.catchup_v1.state import CatchUpV1State
from agents.catchup_v1.configuration import CatchUpV1Configuration
from agents.catchup_v1.nodes import (
    planner_node,
    executor_node,
    communicator_node,
    call_model_node,
    tools_node
)


def should_create_plan(state: CatchUpV1State) -> Literal["planner", "call_model"]:
    """Determine if we should create a plan or go directly to the model.
    
    Creates a plan if:
    - No current plan exists
    - Auto planning is enabled
    - The request seems complex (multiple steps, tool usage needed)
    - User explicitly asks for planning
    """
    
    messages = state.get("messages", [])
    current_plan = state.get("current_plan", [])
    session_config = state.get("session_config", {})
    conversation_phase = state.get("conversation_phase", {})
    
    # If we already have a plan and are in execution phase, skip planning
    if current_plan and conversation_phase.get("phase") in ["executing", "communicating"]:
        return "call_model"
    
    # If auto planning is disabled, skip planning
    if not session_config.get("auto_planning", True):
        return "call_model"
    
    # Check if the latest user message suggests complexity
    if messages:
        latest_message = None
        for msg in reversed(messages):
            if hasattr(msg, 'type') and msg.type == "human":
                latest_message = msg
                break
        
        if latest_message:
            content = latest_message.content.lower()
            
            # Keywords that suggest planning is needed
            planning_keywords = [
                "find", "search", "book", "create", "send", "get", "show", "list",
                "help me", "i need", "can you", "please", "voglio", "cerca", "trova",
                "prenota", "invia", "mostra", "aiutami", "ho bisogno"
            ]
            
            # Complex request indicators
            complex_indicators = [
                "and", "then", "after", "also", "plus", "inoltre", "poi", "dopo", "anche"
            ]
            
            has_planning_keywords = any(keyword in content for keyword in planning_keywords)
            has_complexity = any(indicator in content for indicator in complex_indicators)
            is_long_request = len(content.split()) > 10
            
            if has_planning_keywords and (has_complexity or is_long_request):
                return "planner"
    
    return "call_model"


def should_route_after_model(state: CatchUpV1State) -> Literal["tools", "executor", "communicator", "end"]:
    """Route after model call based on the response and current state."""
    
    messages = state.get("messages", [])
    current_plan = state.get("current_plan", [])
    pending_communications = state.get("pending_communications", [])
    conversation_phase = state.get("conversation_phase", {})
    
    if not messages:
        return "end"
    
    last_message = messages[-1]
    
    # Check for tool calls
    if isinstance(last_message, AIMessage):
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "tools"
        
        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            return "tools"
    
    # Check for pending communications
    if pending_communications:
        return "communicator"
    
    # Check if we have a plan to execute
    if current_plan:
        phase = conversation_phase.get("phase", "")
        
        # If we're in planning phase and just created a plan, move to execution
        if phase == "planning":
            return "executor"
        
        # If we're executing and have pending tasks, continue execution
        if phase == "executing":
            pending_tasks = [task for task in current_plan if task["status"] == "pending"]
            in_progress_tasks = [task for task in current_plan if task["status"] == "in_progress"]
            
            if pending_tasks or in_progress_tasks:
                return "executor"
    
    return "end"


def should_route_after_tools(state: CatchUpV1State) -> Literal["call_model", "executor", "communicator"]:
    """Route after tool execution."""
    
    current_plan = state.get("current_plan", [])
    pending_communications = state.get("pending_communications", [])
    active_task_id = state.get("active_task_id")
    
    # Check for pending communications first
    if pending_communications:
        return "communicator"
    
    # If we have an active task, check if it needs completion
    if active_task_id and current_plan:
        active_task = next((task for task in current_plan if task["id"] == active_task_id), None)
        if active_task and active_task["status"] == "in_progress":
            # Task is still in progress, continue with model
            return "call_model"
    
    # If we have a plan, continue with execution
    if current_plan:
        return "executor"
    
    # Default to model for response
    return "call_model"


def should_route_after_executor(state: CatchUpV1State) -> Literal["call_model", "communicator", "end"]:
    """Route after executor node."""
    
    conversation_phase = state.get("conversation_phase", {})
    pending_communications = state.get("pending_communications", [])
    current_plan = state.get("current_plan", [])
    
    # Check for pending communications
    if pending_communications:
        return "communicator"
    
    # Check conversation phase
    phase = conversation_phase.get("phase", "")
    
    if phase == "closing":
        return "end"
    
    # If we have tasks to execute, continue with model
    if current_plan:
        pending_tasks = [task for task in current_plan if task["status"] == "pending"]
        in_progress_tasks = [task for task in current_plan if task["status"] == "in_progress"]
        
        if pending_tasks or in_progress_tasks:
            return "call_model"
    
    return "end"


def should_route_after_communicator(state: CatchUpV1State) -> Literal["call_model", "executor", "end"]:
    """Route after communicator node."""
    
    current_plan = state.get("current_plan", [])
    conversation_phase = state.get("conversation_phase", {})
    
    # If we have a plan to continue executing
    if current_plan:
        pending_tasks = [task for task in current_plan if task["status"] == "pending"]
        in_progress_tasks = [task for task in current_plan if task["status"] == "in_progress"]
        
        if pending_tasks or in_progress_tasks:
            return "executor"
    
    # Check if we're in closing phase
    if conversation_phase.get("phase") == "closing":
        return "end"
    
    # Continue with model for final response
    return "call_model"


# Create the graph
def create_catchup_v1_graph():
    """Create the CatchUp v1 graph with modern agentic design."""
    
    # Create the state graph
    workflow = StateGraph(CatchUpV1State, config_schema=CatchUpV1Configuration)
    
    # Add nodes
    workflow.add_node("planner", planner_node)
    workflow.add_node("call_model", call_model_node)
    workflow.add_node("tools", tools_node)
    workflow.add_node("executor", executor_node)
    workflow.add_node("communicator", communicator_node)
    
    # Add edges
    # Entry point: decide whether to plan or go directly to model
    workflow.add_conditional_edges(
        START,
        should_create_plan,
        {
            "planner": "planner",
            "call_model": "call_model"
        }
    )
    
    # After planner: go to executor to start execution
    workflow.add_edge("planner", "executor")
    
    # After model: route based on response
    workflow.add_conditional_edges(
        "call_model",
        should_route_after_model,
        {
            "tools": "tools",
            "executor": "executor",
            "communicator": "communicator",
            "end": END
        }
    )
    
    # After tools: route based on state
    workflow.add_conditional_edges(
        "tools",
        should_route_after_tools,
        {
            "call_model": "call_model",
            "executor": "executor",
            "communicator": "communicator"
        }
    )
    
    # After executor: route based on execution state
    workflow.add_conditional_edges(
        "executor",
        should_route_after_executor,
        {
            "call_model": "call_model",
            "communicator": "communicator",
            "end": END
        }
    )
    
    # After communicator: route based on remaining work
    workflow.add_conditional_edges(
        "communicator",
        should_route_after_communicator,
        {
            "call_model": "call_model",
            "executor": "executor",
            "end": END
        }
    )
    
    return workflow


# Create and compile the graph
graph = create_catchup_v1_graph().compile(
    # Add checkpointer for conversation persistence
    # checkpointer=MemorySaver()  # Uncomment when needed
)
