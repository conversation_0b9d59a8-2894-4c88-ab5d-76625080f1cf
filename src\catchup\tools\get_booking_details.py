"""Booking details retrieval tool for user bookings."""

from typing import Optional, List, Tuple
from datetime import datetime, date, time
from langchain_core.tools import tool
from langgraph.config import get_stream_writer

from catchup.supabase.client import supabase
from catchup.Models.model import BookingDetails

@tool
def get_booking_details(
    user_id: str
) -> Tuple[Optional[List[BookingDetails]], Optional[str]]:
    """Fetch current and future booking details from the booking_with_all_details view filtered by user_id.

    This tool queries the Supabase 'booking_with_all_details' view to retrieve
    booking information for a specific user_id. Only bookings that are scheduled for
    the current date and time or later are returned. Bookings are returned in descending
    order by booking_date (latest first).

    Args:
        user_id: The UUID of the user to get booking details for (required)

    Returns:
        Tuple containing (List[BookingDetails], error) where List[BookingDetails] contains
        the current and future booking details data if successful, None if failed.
    """
    try:
        # Try to get stream writer, but handle gracefully if not in runnable context
        try:
            stream_writer = get_stream_writer()
            stream_writer({"custom_tool_call": f"invoking get_booking_details for user_id {user_id}"})
        except Exception:
            # Not in a runnable context, continue without streaming
        
            pass

        # Build the query with user_id filter and ordering
        query = supabase.table('booking_with_all_details').select('*').eq('user_id', user_id).order('booking_date', desc=True)

        response = query.execute()

        # Extract data from response
        if response.data:
            # Get current date and time for comparison
            now = datetime.now()

            # Map database records to BookingDetails models and filter for current/future bookings
            bookings = []
            for item in response.data:
                booking_date_str = item.get('booking_date')
                booking_time_str = item.get('booking_time')

                # Skip bookings without date/time information
                if not booking_date_str or not booking_time_str:
                    continue

                # Parse the date and time from the database
                try:
                    if isinstance(booking_date_str, str):
                        booking_date = datetime.fromisoformat(booking_date_str).date()
                    else:
                        booking_date = booking_date_str

                    if isinstance(booking_time_str, str):
                        booking_time = datetime.fromisoformat(f"1900-01-01T{booking_time_str}").time()
                    else:
                        booking_time = booking_time_str

                    # Combine date and time for comparison
                    booking_datetime = datetime.combine(booking_date, booking_time)

                    # Only include bookings that are scheduled for current date and time or later
                    if booking_datetime >= now:
                        booking = BookingDetails(
                            booking_id=item.get('booking_id'),
                            deal_title=item.get('deal_title'),
                            deal_description=item.get('deal_description'),
                            business_name=item.get('business_name'),
                            business_address=item.get('business_address'),
                            booking_date=booking_date,
                            booking_time=booking_time,
                            first_name=item.get('first_name'),
                            last_name=item.get('last_name'),
                            email=item.get('email'),
                            user_id=item.get('user_id'),
                            fake=item.get('fake')
                        )
                        bookings.append(booking)

                except (ValueError, TypeError) as e:
                    # Skip bookings with invalid date/time format
                    print(f"Skipping booking with invalid date/time format: {e}")
                    continue

            return bookings, None
        else:
            # No data returned - return empty list instead of None for consistency
            return [], None

    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)
