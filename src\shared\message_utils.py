
from typing import List
from langchain_core.messages import  BaseMessage

def limit_message_history(messages: List[BaseMessage], memory_length: int) -> List[BaseMessage]:
    """Limit message history to the specified number of recent messages.

    Args:
        messages: List of conversation messages
        memory_length: Maximum number of messages to keep

    Returns:
        List of messages limited to the specified length
    """
    if memory_length <= 0:
        return []

    # Keep only the most recent messages up to the memory limit
    return messages[-memory_length:]
