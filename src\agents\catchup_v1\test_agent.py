"""Test script for CatchUp v1 agent."""

import asyncio
from datetime import datetime
from langchain_core.messages import HumanMessage

from catchup_v1.graph import graph
from catchup_v1.state import create_initial_state
from catchup_v1.configuration import CatchUpV1Configuration


async def test_catchup_v1():
    """Test the CatchUp v1 agent with a sample conversation."""
    
    print("🎯 Testing CatchUp v1 - Modern AI Agentic Design")
    print("=" * 50)
    
    # Create initial state
    initial_state = create_initial_state(
        user_id="test_user_123",
        session_id=f"test_session_{int(datetime.now().timestamp())}",
        email_address="<EMAIL>",
        phone_number="+1234567890",
        latitude="45.4666",
        longitude="9.1832",
        memory_length=15
    )
    
    # Add a test message
    initial_state["messages"] = [
        HumanMessage(content="Ciao! Sto cercando un ristorante italiano nella mia zona per stasera. Puoi aiutarmi a trovare qualcosa di buono e poi prenotare per 2 persone alle 20:00?")
    ]
    
    # Configuration
    config = {
        "configurable": {
            "model_name": "openai/gpt-4o-mini",
            "model_temperature": 0.1,
            "auto_planning_enabled": True,
            "communication_enabled": True,
            "streaming_enabled": True
        }
    }
    
    print(f"Initial State:")
    print(f"- User ID: {initial_state['user_context']['user_id']}")
    print(f"- Session ID: {initial_state['session_config']['session_id']}")
    print(f"- Location: {initial_state['user_context']['location']}")
    print(f"- Auto Planning: {initial_state['session_config']['auto_planning']}")
    print()
    
    try:
        print("🚀 Starting agent execution...")
        print("-" * 30)
        
        # Run the agent
        result = await graph.ainvoke(initial_state, config)
        
        print("\n📋 Final State Summary:")
        print("-" * 30)
        
        # Display conversation
        messages = result.get("messages", [])
        print(f"Messages: {len(messages)}")
        for i, msg in enumerate(messages[-3:], 1):  # Show last 3 messages
            msg_type = "Human" if hasattr(msg, 'type') and msg.type == "human" else "AI"
            content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            print(f"  {i}. [{msg_type}]: {content}")
        
        # Display plan
        current_plan = result.get("current_plan", [])
        if current_plan:
            print(f"\nPlan: {len(current_plan)} tasks")
            for i, task in enumerate(current_plan, 1):
                status_emoji = {"pending": "⏳", "in_progress": "🔄", "completed": "✅", "failed": "❌"}.get(task["status"], "❓")
                print(f"  {i}. {status_emoji} {task['content']} [{task['priority']}]")
        
        # Display progress
        progress = result.get("progress_metrics", {})
        if progress:
            print(f"\nProgress:")
            print(f"  - Total tasks: {progress.get('total_tasks', 0)}")
            print(f"  - Completed: {progress.get('completed_tasks', 0)}")
            print(f"  - Failed: {progress.get('failed_tasks', 0)}")
            print(f"  - Tools used: {', '.join(progress.get('tools_used', []))}")
        
        # Display communication
        comm_log = result.get("communication_log", [])
        if comm_log:
            print(f"\nCommunications: {len(comm_log)}")
            for comm in comm_log[-3:]:  # Show last 3
                print(f"  - {comm['type']} to {comm['recipient']}: {comm['status']}")
        
        # Display phase
        phase = result.get("conversation_phase", {})
        if phase:
            print(f"\nConversation Phase: {phase.get('phase', 'unknown')} (confidence: {phase.get('confidence', 0):.1f})")
        
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_simple_query():
    """Test with a simple query that shouldn't trigger planning."""
    
    print("\n🔍 Testing simple query (no planning expected)")
    print("=" * 50)
    
    # Create initial state
    initial_state = create_initial_state(
        user_id="test_user_simple",
        session_id=f"simple_session_{int(datetime.now().timestamp())}",
        email_address="<EMAIL>"
    )
    
    # Simple greeting
    initial_state["messages"] = [
        HumanMessage(content="Ciao! Come stai?")
    ]
    
    # Configuration with planning disabled for this test
    config = {
        "configurable": {
            "model_name": "openai/gpt-4o-mini",
            "auto_planning_enabled": False,
            "communication_enabled": False
        }
    }
    
    try:
        result = await graph.ainvoke(initial_state, config)
        
        messages = result.get("messages", [])
        last_message = messages[-1] if messages else None
        
        if last_message:
            print(f"Response: {last_message.content}")
        
        # Should not have created a plan
        plan = result.get("current_plan", [])
        print(f"Plan created: {'Yes' if plan else 'No'} ({len(plan)} tasks)")
        
        print("✅ Simple query test completed!")
        
    except Exception as e:
        print(f"❌ Simple query test failed: {str(e)}")


if __name__ == "__main__":
    print("Starting CatchUp v1 Tests...")
    
    # Run tests
    asyncio.run(test_catchup_v1())
    asyncio.run(test_simple_query())
    
    print("\n🎉 All tests completed!")
