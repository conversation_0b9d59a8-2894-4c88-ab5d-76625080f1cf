import pytest
from langchain_core.messages import HumanMessage

from catchup import graph

pytestmark = pytest.mark.anyio


@pytest.mark.langsmith
async def test_chatbot_with_session_memory() -> None:
    """Test chatbot with session-based memory using session_id."""
    session_id = "test_session_123"
    config = {
        "configurable": {
            "thread_id": session_id,  # Use session_id as thread_id
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful assistant."
        }
    }
    
    # First message with session context
    inputs1 = {
        "messages": [HumanMessage(content="My name is <PERSON>")],
        "session_id": session_id,
        "user_id": "user_123",
        "memory_lenght": "10"
    }
    res1 = await graph.ainvoke(inputs1, config)
    
    # Second message - should remember Alice
    inputs2 = {
        "messages": [HumanMessage(content="What's my name?")],
        "session_id": session_id,
        "memory_lenght": "10"
    }
    res2 = await graph.ainvoke(inputs2, config)
    
    assert res2 is not None
    assert "Alice" in str(res2["messages"][-1].content)


@pytest.mark.langsmith
async def test_chatbot_memory_limiting() -> None:
    """Test dynamic memory limiting with session persistence."""
    session_id = "test_memory_session"
    config = {
        "configurable": {
            "thread_id": session_id,
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful assistant."
        }
    }
    
    # Send multiple messages to build history
    for i in range(5):
        inputs = {
            "messages": [HumanMessage(content=f"Message {i+1}")],
            "session_id": session_id,
            "memory_lenght": "3"  # Limit to 3 messages
        }
        await graph.ainvoke(inputs, config)
    
    # Final test - should only remember last 3 exchanges
    inputs_final = {
        "messages": [HumanMessage(content="How many messages do you remember?")],
        "session_id": session_id,
        "memory_lenght": "3"
    }
    res = await graph.ainvoke(inputs_final, config)
    
    assert res is not None


@pytest.mark.langsmith
async def test_different_sessions_isolated() -> None:
    """Test that different session_ids maintain separate conversations."""
    config_base = {
        "configurable": {
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful assistant."
        }
    }
    
    # Session 1
    session1_id = "session_alice"
    config1 = {**config_base, "configurable": {**config_base["configurable"], "thread_id": session1_id}}
    inputs1 = {
        "messages": [HumanMessage(content="My name is Alice")],
        "session_id": session1_id
    }
    await graph.ainvoke(inputs1, config1)
    
    # Session 2
    session2_id = "session_bob"
    config2 = {**config_base, "configurable": {**config_base["configurable"], "thread_id": session2_id}}
    inputs2 = {
        "messages": [HumanMessage(content="My name is Bob")],
        "session_id": session2_id
    }
    await graph.ainvoke(inputs2, config2)
    
    # Test isolation - Alice session shouldn't know about Bob
    inputs_test = {
        "messages": [HumanMessage(content="What's my name?")],
        "session_id": session1_id
    }
    res = await graph.ainvoke(inputs_test, config1)
    
    assert "Alice" in str(res["messages"][-1].content)
    assert "Bob" not in str(res["messages"][-1].content)

